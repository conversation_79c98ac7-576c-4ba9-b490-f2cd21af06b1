('D:\\tool\\python\\bot4\\build\\social_advanced\\social_advanced.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\tool\\python\\bot4\\build\\social_advanced\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\tool\\python\\bot4\\build\\social_advanced\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\tool\\python\\bot4\\build\\social_advanced\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\tool\\python\\bot4\\build\\social_advanced\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\tool\\python\\bot4\\build\\social_advanced\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\tool\\python\\bot4\\build\\social_advanced\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('social_advanced_obfuscated',
   'D:\\tool\\python\\bot4\\social_advanced_obfuscated.py',
   'PYSOURCE'),
  ('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_aes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_curve25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ctr.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Cryptodome\\Math\\_modexp.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Math\\_modexp.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_arc2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_eksblowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA256.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ofb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_pkcs1_decode.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA512.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ecb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_ed25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ocb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_cbc.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_cast.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_poly1305.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA1.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_ghash_clmul.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_keccak.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_ec_ws.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_aesni.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_MD2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_ghash_portable.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_MD4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_des.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_ARC4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Cryptodome\\Util\\_strxor.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\_strxor.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_BLAKE2b.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Cryptodome\\Protocol\\_scrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_BLAKE2s.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_Salsa20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_MD5.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_ed448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA384.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Cryptodome\\Util\\_cpuid_c.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_RIPEMD160.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_chacha20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_cfb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_blowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA224.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_curve448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_des3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\win32crypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32crypt.pyd',
   'EXTENSION'),
  ('lz4\\_version.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4\\_version.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lz4\\block\\_block.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4\\block\\_block.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('lz4-4.4.4.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\top_level.txt',
   'DATA'),
  ('lz4-4.4.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\WHEEL',
   'DATA'),
  ('lz4-4.4.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\METADATA',
   'DATA'),
  ('lz4-4.4.4.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('lz4-4.4.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\RECORD',
   'DATA'),
  ('lz4-4.4.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\lz4-4.4.4.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'D:\\tool\\python\\bot4\\build\\social_advanced\\base_library.zip',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
