#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import time
import sys
from pathlib import Path

def test_executable(exe_path, timeout=10):
    """Test if executable runs without crashing"""
    print(f"🧪 Testing {exe_path}...")
    
    if not os.path.exists(exe_path):
        print(f"❌ File not found: {exe_path}")
        return False
    
    try:
        # Get file size
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"📊 File size: {file_size:.1f} MB")
        
        # Try to run the executable
        print(f"🚀 Starting {os.path.basename(exe_path)}...")
        
        # Start process
        process = subprocess.Popen(
            [exe_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
        )
        
        # Wait a bit to see if it starts properly
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print(f"✅ {os.path.basename(exe_path)} started successfully")
            print(f"🔄 Process is running (PID: {process.pid})")
            
            # Let it run for a few more seconds
            time.sleep(2)
            
            # Terminate the process
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"🛑 Process terminated cleanly")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"🔪 Process killed (timeout)")
            
            return True
        else:
            # Process exited
            stdout, stderr = process.communicate()
            return_code = process.returncode
            
            print(f"❌ Process exited with code: {return_code}")
            if stdout:
                print(f"📤 STDOUT: {stdout.decode()[:200]}...")
            if stderr:
                print(f"📤 STDERR: {stderr.decode()[:200]}...")
            
            return False
            
    except Exception as e:
        print(f"❌ Error testing {exe_path}: {e}")
        return False

def check_dependencies():
    """Check if all required files exist"""
    print("🔍 Checking dependencies...")
    
    required_files = [
        "social.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print("✅ All required files present")
        return True

def analyze_executables():
    """Analyze the generated executables"""
    print("\n📊 Executable Analysis")
    print("=" * 40)
    
    exe_files = [
        "dist/SocialTool.exe",
        "dist/social_advanced.exe"
    ]
    
    for exe_path in exe_files:
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path)
            file_size_mb = file_size / (1024 * 1024)
            
            print(f"\n📁 {os.path.basename(exe_path)}")
            print(f"   Size: {file_size_mb:.1f} MB ({file_size:,} bytes)")
            
            # Get file creation time
            creation_time = os.path.getctime(exe_path)
            creation_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(creation_time))
            print(f"   Created: {creation_str}")
            
            # Try to get file version info (Windows only)
            if os.name == 'nt':
                try:
                    import win32api
                    info = win32api.GetFileVersionInfo(exe_path, "\\")
                    ms = info['FileVersionMS']
                    ls = info['FileVersionLS']
                    version = f"{win32api.HIWORD(ms)}.{win32api.LOWORD(ms)}.{win32api.HIWORD(ls)}.{win32api.LOWORD(ls)}"
                    print(f"   Version: {version}")
                except:
                    print(f"   Version: N/A")
        else:
            print(f"\n❌ {exe_path} not found")

def main():
    """Main test function"""
    print("🧪 Executable Testing Tool")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed")
        return
    
    # Analyze executables
    analyze_executables()
    
    # Test executables
    print("\n🧪 Testing Executables")
    print("=" * 40)
    
    exe_files = [
        "dist/SocialTool.exe",
        "dist/social_advanced.exe"
    ]
    
    results = {}
    
    for exe_path in exe_files:
        print(f"\n{'='*50}")
        result = test_executable(exe_path)
        results[exe_path] = result
        print(f"{'='*50}")
    
    # Summary
    print(f"\n📋 Test Summary")
    print("=" * 40)
    
    for exe_path, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{os.path.basename(exe_path)}: {status}")
    
    # Overall result
    all_passed = all(results.values())
    if all_passed:
        print(f"\n🎉 All tests passed!")
        print(f"📁 Your executables are ready to use")
    else:
        print(f"\n⚠️ Some tests failed")
        print(f"💡 Check the error messages above")

if __name__ == "__main__":
    main()
