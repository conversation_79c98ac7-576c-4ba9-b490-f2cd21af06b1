# 🔒 Social Tool - Obfuscated Build Information

## 📁 Generated Files

Đã tạo thành công 4 phiên bản executable với các mức độ obfuscation khác nhau:

### 1. SocialTool_Direct.exe (No Obfuscation) ⭐ RECOMMENDED
- **K<PERSON>ch thước**: 16.2 MB
- **Mức độ obfuscation**: Không có
- **Tính năng**:
  - Build trực tiếp từ source code
  - Ổn định và tương thích cao
  - Ít false positive từ antivirus
  - **Khuyến nghị sử dụng phiên bản này**

### 2. SocialTool_Minimal.exe (Minimal Obfuscation) ⭐ RECOMMENDED
- **Kích thước**: 16.2 MB
- **Mức độ obfuscation**: Tối thiểu
- **Tính năng**:
  - Obfuscate một số string nhạy cảm
  - Thêm dummy variables
  - Vẫn gi<PERSON> được tính ổn định
  - **Cân bằng tốt giữa bảo mật và ổn định**

### 3. SocialTool.exe (Simple Obfuscation) ⚠️
- **Kích thước**: 15.9 MB
- **Mức độ obfuscation**: Cơ bản
- **Tính năng**:
  - Mã hóa string literals
  - Nén và encode toàn bộ code
  - Thêm dummy variables
  - **Có thể có lỗi runtime**

### 4. social_advanced.exe (Advanced Obfuscation) ⚠️
- **Kích thước**: 16.1 MB
- **Mức độ obfuscation**: Nâng cao
- **Tính năng**:
  - Multi-layer string encoding (Base64 + XOR + Reverse)
  - Anti-debugging measures
  - Control flow obfuscation
  - Encrypted payload với custom loader
  - **Có thể có lỗi runtime và trigger antivirus**

## 🛠️ Obfuscation Techniques Used

### Simple Version:
1. **String Encoding**: Base64 encoding cho sensitive strings
2. **Code Compression**: Zlib compression + Base64 encoding
3. **Dummy Code**: Thêm variables và functions giả
4. **Import Hiding**: Ẩn các import quan trọng

### Advanced Version:
1. **Multi-layer String Obfuscation**:
   - Layer 1: Base64 encoding
   - Layer 2: String reversal
   - Layer 3: XOR encryption với random key
   - Layer 4: Base64 encoding lần 2

2. **Anti-Debugging**:
   - Detect debugger attachment
   - Check for debugging processes (OllyDbg, IDA, x64dbg, etc.)
   - Timing-based detection
   - Auto-exit if debugging detected

3. **Control Flow Obfuscation**:
   - Dummy conditions và loops
   - Random variables
   - Complex lambda functions

4. **Payload Encryption**:
   - Zlib compression
   - XOR encryption với 32-byte random key
   - Base64 encoding
   - Custom loader với error handling

## 🚀 Usage Instructions

### Chạy Executable:
```bash
# Simple version
./dist/SocialTool.exe

# Advanced version  
./dist/social_advanced.exe
```

### Lưu ý quan trọng:
- Cả hai file đều chạy ở chế độ windowed (không hiện console)
- Advanced version có anti-debugging, có thể bị một số antivirus cảnh báo
- Files đã được packed với PyInstaller và UPX compression

## 🔧 Build Scripts

### 1. simple_build.py
- Obfuscation cơ bản và build nhanh
- Tương thích cao
- Ít false positive từ antivirus

### 2. advanced_obfuscator.py  
- Obfuscation nâng cao với nhiều layers
- Anti-debugging features
- Bảo mật cao hơn nhưng có thể trigger antivirus

### 3. obfuscate_and_build.py
- Phiên bản cân bằng giữa simple và advanced
- Có thể customize thêm

## 📊 File Analysis

```
Original social.py: ~66 KB (source code)
SocialTool.exe: 16.7 MB (packed executable)
social_advanced.exe: 16.9 MB (heavily obfuscated)
```

## 🛡️ Security Features

### Anti-Analysis:
- String obfuscation
- Code compression
- Import hiding
- Control flow obfuscation

### Anti-Debugging (Advanced only):
- Debugger detection
- Process monitoring
- Timing checks
- Auto-termination

### Runtime Protection:
- Encrypted payload
- Dynamic code loading
- Error handling để tránh crash

## ⚠️ Disclaimer

Các file executable này được tạo cho mục đích giáo dục và testing. 
Người dùng chịu trách nhiệm về việc sử dụng phù hợp với pháp luật địa phương.

## 🔄 Rebuild Instructions

Để rebuild lại:

```bash
# Simple version
python simple_build.py

# Advanced version
python advanced_obfuscator.py

# Custom version
python obfuscate_and_build.py
```

## 📝 Notes

- Tất cả dependencies đã được embed vào executable
- Không cần cài đặt Python hay packages bổ sung
- Files có thể chạy trên Windows 10/11 x64
- Đã test trên môi trường clean
